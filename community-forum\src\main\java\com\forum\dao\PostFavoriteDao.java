package com.forum.dao;

import com.forum.entity.PostFavorite;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * PostFavorite DAO interface
 */
public interface PostFavoriteDao {
    
    /**
     * Get post favorite by post ID and user ID
     * 
     * @param postId Post ID
     * @param userId User ID
     * @return PostFavorite object
     */
    PostFavorite getPostFavorite(@Param("postId") Long postId, @Param("userId") Long userId);
    
    /**
     * Insert a new post favorite
     * 
     * @param postFavorite PostFavorite object
     * @return Number of rows affected
     */
    int insertPostFavorite(PostFavorite postFavorite);
    
    /**
     * Delete post favorite
     * 
     * @param postId Post ID
     * @param userId User ID
     * @return Number of rows affected
     */
    int deletePostFavorite(@Param("postId") Long postId, @Param("userId") Long userId);
    
    /**
     * Get post favorites by post ID
     * 
     * @param postId Post ID
     * @return PostFavorite list
     */
    List<PostFavorite> getPostFavoritesByPostId(Long postId);
    
    /**
     * Get post favorites by user ID
     * 
     * @param userId User ID
     * @param offset Offset
     * @param limit Limit
     * @return PostFavorite list
     */
    List<PostFavorite> getPostFavoritesByUserId(@Param("userId") Long userId, 
                                              @Param("offset") int offset, 
                                              @Param("limit") int limit);
    
    /**
     * Get post favorite count by post ID
     * 
     * @param postId Post ID
     * @return Post favorite count
     */
    int getPostFavoriteCountByPostId(Long postId);
    
    /**
     * Get post favorite count by user ID
     * 
     * @param userId User ID
     * @return Post favorite count
     */
    int getPostFavoriteCountByUserId(Long userId);
} 
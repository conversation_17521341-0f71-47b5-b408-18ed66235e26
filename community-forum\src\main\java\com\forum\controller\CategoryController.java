package com.forum.controller;

import com.forum.entity.Category;
import com.forum.service.CategoryService;
import com.forum.util.Constant;
import com.forum.util.PageResult;
import com.forum.util.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 分类控制器
 */
@RestController
@RequestMapping("/api/category")
public class CategoryController {
    
    @Autowired
    private CategoryService categoryService;
    
    /**
     * 获取所有分类
     */
    @GetMapping("/all")
    public Result<List<Category>> getAllCategories() {
        List<Category> categories = categoryService.getAllCategories();
        return Result.success(categories);
    }
    
    /**
     * 获取启用的分类
     */
    @GetMapping("/enabled")
    public Result<List<Category>> getEnabledCategories() {
        List<Category> categories = categoryService.getEnabledCategories();
        return Result.success(categories);
    }
    
    /**
     * 获取分类列表（分页）
     */
    @GetMapping("/list")
    public Result<PageResult<Category>> getCategoryList(@RequestParam(defaultValue = "1") int pageNum,
                                                       @RequestParam(defaultValue = "10") int pageSize) {
        PageResult<Category> pageResult = categoryService.getCategoryList(pageNum, pageSize);
        return Result.success(pageResult);
    }
    
    /**
     * 根据ID获取分类
     */
    @GetMapping("/{categoryId}")
    public Result<Category> getCategoryById(@PathVariable Integer categoryId) {
        Category category = categoryService.getCategoryById(categoryId);
        return Result.success(category);
    }
    
    /**
     * 添加分类（管理员）
     */
    @PostMapping
    public Result<Category> addCategory(@RequestBody Category category, @RequestAttribute("userRole") String userRole) {
        // 检查权限
        if (!Constant.User.ROLE_ADMIN.equals(userRole)) {
            return Result.error(Result.ResultCode.FORBIDDEN.getCode(), "没有权限执行此操作");
        }
        
        Category addedCategory = categoryService.addCategory(category);
        return Result.success("分类添加成功", addedCategory);
    }
    
    /**
     * 更新分类（管理员）
     */
    @PutMapping("/{categoryId}")
    public Result<Category> updateCategory(@PathVariable Integer categoryId, 
                                          @RequestBody Category category,
                                          @RequestAttribute("userRole") String userRole) {
        // 检查权限
        if (!Constant.User.ROLE_ADMIN.equals(userRole)) {
            return Result.error(Result.ResultCode.FORBIDDEN.getCode(), "没有权限执行此操作");
        }
        
        category.setCategoryId(categoryId);
        Category updatedCategory = categoryService.updateCategory(category);
        return Result.success("分类更新成功", updatedCategory);
    }
    
    /**
     * 删除分类（管理员）
     */
    @DeleteMapping("/{categoryId}")
    public Result<Boolean> deleteCategory(@PathVariable Integer categoryId, @RequestAttribute("userRole") String userRole) {
        // 检查权限
        if (!Constant.User.ROLE_ADMIN.equals(userRole)) {
            return Result.error(Result.ResultCode.FORBIDDEN.getCode(), "没有权限执行此操作");
        }
        
        boolean result = categoryService.deleteCategory(categoryId);
        return Result.success("分类删除成功", result);
    }
}
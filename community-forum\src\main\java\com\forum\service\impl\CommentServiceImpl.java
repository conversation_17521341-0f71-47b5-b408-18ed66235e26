package com.forum.service.impl;

import com.forum.dao.CommentDao;
import com.forum.dao.PostDao;
import com.forum.entity.Comment;
import com.forum.entity.Post;
import com.forum.exception.BusinessException;
import com.forum.exception.ForbiddenException;
import com.forum.exception.NotFoundException;
import com.forum.service.CommentService;
import com.forum.service.NotificationService;
import com.forum.util.Constant;
import com.forum.util.PageResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 评论服务实现类
 */
@Service
public class CommentServiceImpl implements CommentService {
    
    private static final Logger logger = LoggerFactory.getLogger(CommentServiceImpl.class);
    
    @Autowired
    private CommentDao commentDao;
    
    @Autowired
    private PostDao postDao;
    
    @Autowired
    private NotificationService notificationService;
    
    @Override
    @Transactional
    public Comment addComment(Comment comment) {
        // 检查帖子是否存在
        Post post = postDao.getPostById(comment.getPostId());
        if (post == null) {
            throw new NotFoundException("帖子不存在");
        }
        
        // 设置默认值
        Date now = new Date();
        comment.setStatus(Constant.Comment.STATUS_NORMAL);
        comment.setCreateTime(now);
        comment.setUpdateTime(now);
        
        // 插入数据库
        commentDao.insertComment(comment);
        
        // 更新帖子评论数
        postDao.updateCommentCount(comment.getPostId(), 1);
        
        // 发送通知
        // 如果是回复评论，通知被回复的用户
        if (comment.getParentId() != null && comment.getToUserId() != null) {
            notificationService.sendCommentNotification(
                comment.getUserId(), 
                comment.getToUserId(), 
                comment.getPostId(), 
                comment.getCommentId()
            );
        } 
        // 如果是直接评论帖子，通知帖子作者
        else if (!comment.getUserId().equals(post.getUserId())) {
            notificationService.sendCommentNotification(
                comment.getUserId(), 
                post.getUserId(), 
                comment.getPostId(), 
                comment.getCommentId()
            );
        }
        
        logger.info("评论发表成功: {}", comment.getCommentId());
        return comment;
    }
    
    @Override
    @Transactional
    public Comment updateComment(Comment comment) {
        // 检查评论是否存在
        Comment existingComment = commentDao.getCommentById(comment.getCommentId());
        if (existingComment == null) {
            throw new NotFoundException("评论不存在");
        }
        
        // 检查是否有权限修改
        if (!existingComment.getUserId().equals(comment.getUserId())) {
            throw new ForbiddenException("无权修改此评论");
        }
        
        // 设置更新时间
        comment.setUpdateTime(new Date());
        
        // 更新数据库
        commentDao.updateComment(comment);
        
        // 重新获取评论信息
        Comment updatedComment = commentDao.getCommentById(comment.getCommentId());
        
        logger.info("评论更新成功: {}", comment.getCommentId());
        return updatedComment;
    }
    
    @Override
    @Transactional
    public boolean deleteComment(Long commentId, Long userId) {
        // 检查评论是否存在
        Comment comment = commentDao.getCommentById(commentId);
        if (comment == null) {
            throw new NotFoundException("评论不存在");
        }
        
        // 检查是否有权限删除（评论作者或帖子作者可以删除评论）
        Post post = postDao.getPostById(comment.getPostId());
        if (!comment.getUserId().equals(userId) && !post.getUserId().equals(userId)) {
            throw new ForbiddenException("无权删除此评论");
        }
        
        // 删除评论（逻辑删除）
        commentDao.deleteComment(commentId);
        
        // 更新帖子评论数
        postDao.updateCommentCount(comment.getPostId(), -1);
        
        logger.info("评论删除成功: {}", commentId);
        return true;
    }
    
    @Override
    public Comment getCommentById(Long commentId) {
        Comment comment = commentDao.getCommentById(commentId);
        if (comment == null) {
            throw new NotFoundException("评论不存在");
        }
        return comment;
    }
    
    @Override
    public PageResult<Comment> getCommentsByPostId(Long postId, int pageNum, int pageSize) {
        // 计算偏移量
        int offset = (pageNum - 1) * pageSize;
        
        // 查询评论列表
        List<Comment> commentList = commentDao.getCommentsByPostId(postId, offset, pageSize);
        
        // 查询总数
        int total = commentDao.getCommentCountByPostId(postId);
        
        return new PageResult<>(pageNum, pageSize, total, commentList);
    }
    
    @Override
    public List<Comment> getChildComments(Long parentId) {
        return commentDao.getChildComments(parentId);
    }
    
    @Override
    public PageResult<Comment> getCommentsByUserId(Long userId, int pageNum, int pageSize) {
        // 计算偏移量
        int offset = (pageNum - 1) * pageSize;
        
        // 查询评论列表
        List<Comment> commentList = commentDao.getCommentsByUserId(userId, offset, pageSize);
        
        // 查询总数
        int total = commentDao.getCommentCountByUserId(userId);
        
        return new PageResult<>(pageNum, pageSize, total, commentList);
    }
}
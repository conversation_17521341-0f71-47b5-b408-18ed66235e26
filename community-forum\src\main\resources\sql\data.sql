-- Use database
USE community_forum;

-- Insert sample users
-- Password is '123456' encrypted with BCrypt
INSERT INTO `user` (`username`, `password`, `email`, `nickname`, `avatar`, `bio`, `status`, `role`, `create_time`, `update_time`) 
VALUES 
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVKIUi', '<EMAIL>', '管理员', '/static/images/avatar/admin.jpg', '系统管理员', 1, 'ADMIN', NOW(), NOW()),
('user1', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVKIUi', '<EMAIL>', '用户一', '/static/images/avatar/user1.jpg', '我是用户一', 1, 'USER', NOW(), NOW()),
('user2', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVKIUi', '<EMAIL>', '用户二', '/static/images/avatar/user2.jpg', '我是用户二', 1, 'USER', NOW(), NOW());

-- Insert sample categories
INSERT INTO `category` (`name`, `description`, `icon`, `sort_order`, `status`, `create_time`, `update_time`) 
VALUES 
('公告', '管理员的官方公告', 'fa-bullhorn', 1, 1, NOW(), NOW()),
('综合讨论', '一般讨论话题', 'fa-comments', 2, 1, NOW(), NOW()),
('技术', '技术相关讨论', 'fa-laptop', 3, 1, NOW(), NOW()),
('帮助与支持', '从社区获取帮助', 'fa-life-ring', 4, 1, NOW(), NOW());

-- Insert sample posts
INSERT INTO `post` (`title`, `content`, `user_id`, `category_id`, `view_count`, `like_count`, `comment_count`, `status`, `create_time`, `update_time`) 
VALUES 
('欢迎来到我们的社区论坛！', '<p>欢迎来到我们的新社区论坛！这是一个您可以讨论各种话题、分享想法和与其他成员交流的地方。</p><p>请确保遵守我们的社区准则并尊重他人。</p>', 1, 1, 120, 15, 5, 2, NOW(), NOW()),
('如何使用这个论坛？', '<p>以下是一些有效使用本论坛的提示：</p><ul><li>使用搜索功能查找现有主题</li><li>为您的帖子选择适当的分类</li><li>在您的帖子中保持清晰简洁</li><li>使用格式化使您的帖子更易读</li></ul>', 1, 1, 85, 10, 3, 1, NOW(), NOW()),
('自我介绍', '<p>大家好！我是新来的，想介绍一下自己。我对技术、编程和设计很感兴趣。</p><p>期待与社区互动！</p>', 2, 2, 42, 7, 2, 1, NOW(), NOW()),
('我应该先学习哪种编程语言？', '<p>我是编程初学者，想知道首先学习哪种编程语言最好。我对网页开发很感兴趣。</p><p>感谢任何建议！</p>', 3, 3, 65, 3, 4, 1, NOW(), NOW());

-- Insert sample comments
INSERT INTO `comment` (`content`, `post_id`, `user_id`, `parent_id`, `to_user_id`, `status`, `create_time`, `update_time`) 
VALUES 
('感谢热情欢迎！', 1, 2, NULL, NULL, 1, NOW(), NOW()),
('这些信息非常有用。', 2, 3, NULL, NULL, 1, NOW(), NOW()),
('欢迎加入社区！有任何问题随时提问。', 3, 1, NULL, NULL, 1, NOW(), NOW()),
('对于网页开发，我建议从JavaScript开始学习。', 4, 1, NULL, NULL, 1, NOW(), NOW()),
('我同意管理员的看法。JavaScript是一个很好的起点。', 4, 2, 4, 1, 1, NOW(), NOW());

-- Insert sample likes
INSERT INTO `post_like` (`post_id`, `user_id`, `create_time`) 
VALUES 
(1, 2, NOW()),
(1, 3, NOW()),
(2, 2, NOW()),
(3, 1, NOW()),
(4, 1, NOW());

-- Insert sample favorites
INSERT INTO `post_favorite` (`post_id`, `user_id`, `create_time`) 
VALUES 
(1, 3, NOW()),
(2, 3, NOW()),
(3, 1, NOW());

-- Insert sample notifications
INSERT INTO `notification` (`type`, `sender_id`, `receiver_id`, `post_id`, `comment_id`, `content`, `is_read`, `create_time`) 
VALUES 
('COMMENT', 2, 1, 1, 1, '用户一评论了您的帖子"欢迎来到我们的社区论坛！"', 0, NOW()),
('LIKE', 2, 1, 1, NULL, '用户一点赞了您的帖子"欢迎来到我们的社区论坛！"', 0, NOW()),
('SYSTEM', NULL, 1, NULL, NULL, '欢迎来到社区论坛！请完善您的个人资料。', 0, NOW()),
('COMMENT', 1, 3, 4, 4, '管理员评论了您的帖子"我应该先学习哪种编程语言？"', 0, NOW()); 
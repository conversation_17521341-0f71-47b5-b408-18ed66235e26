package com.forum.service;

import com.forum.entity.Post;
import com.forum.util.PageResult;

/**
 * 帖子服务接口
 */
public interface PostService {
    
    /**
     * 发布帖子
     * 
     * @param post 帖子信息
     * @return 发布成功的帖子
     */
    Post publishPost(Post post);
    
    /**
     * 更新帖子
     * 
     * @param post 帖子信息
     * @return 更新后的帖子
     */
    Post updatePost(Post post);
    
    /**
     * 删除帖子
     * 
     * @param postId 帖子ID
     * @param userId 用户ID（用于权限检查）
     * @return 是否成功
     */
    boolean deletePost(Long postId, Long userId);
    
    /**
     * 根据ID获取帖子
     * 
     * @param postId 帖子ID
     * @return 帖子信息
     */
    Post getPostById(Long postId);
    
    /**
     * 获取帖子列表（分页）
     * 
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 帖子分页列表
     */
    PageResult<Post> getPostList(int pageNum, int pageSize);
    
    /**
     * 根据分类获取帖子列表（分页）
     * 
     * @param categoryId 分类ID
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 帖子分页列表
     */
    PageResult<Post> getPostListByCategory(Integer categoryId, int pageNum, int pageSize);
    
    /**
     * 根据用户获取帖子列表（分页）
     * 
     * @param userId 用户ID
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 帖子分页列表
     */
    PageResult<Post> getPostListByUser(Long userId, int pageNum, int pageSize);
    
    /**
     * 搜索帖子（分页）
     * 
     * @param keyword 关键词
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 帖子分页列表
     */
    PageResult<Post> searchPosts(String keyword, int pageNum, int pageSize);
    
    /**
     * 增加帖子浏览量
     * 
     * @param postId 帖子ID
     */
    void incrementViewCount(Long postId);
    
    /**
     * 点赞帖子
     * 
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean likePost(Long postId, Long userId);
    
    /**
     * 取消点赞
     * 
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean unlikePost(Long postId, Long userId);
    
    /**
     * 收藏帖子
     * 
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean favoritePost(Long postId, Long userId);
    
    /**
     * 取消收藏
     * 
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean unfavoritePost(Long postId, Long userId);
    
    /**
     * 检查用户是否已点赞帖子
     * 
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 是否已点赞
     */
    boolean isPostLiked(Long postId, Long userId);
    
    /**
     * 检查用户是否已收藏帖子
     * 
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 是否已收藏
     */
    boolean isPostFavorited(Long postId, Long userId);
} 
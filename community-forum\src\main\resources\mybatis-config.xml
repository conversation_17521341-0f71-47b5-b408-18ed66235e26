<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <!-- Global settings -->
    <settings>
        <!-- Use jdbc getGeneratedKeys to get the primary key value -->
        <setting name="useGeneratedKeys" value="true"/>
        <!-- Use column label instead of column name -->
        <setting name="useColumnLabel" value="true"/>
        <!-- Enable camel case mapping from DB column names to Java properties -->
        <setting name="mapUnderscoreToCamelCase" value="true"/>
        <!-- Log SQL statements -->
        <setting name="logImpl" value="SLF4J"/>
    </settings>
    
    <!-- Type aliases for entity classes -->
    <typeAliases>
        <package name="com.forum.entity"/>
    </typeAliases>
</configuration> 
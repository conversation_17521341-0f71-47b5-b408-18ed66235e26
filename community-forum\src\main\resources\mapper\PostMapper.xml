<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.forum.dao.PostDao">
    <!-- 结果映射 -->
    <resultMap id="postResultMap" type="Post">
        <id property="postId" column="post_id"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="userId" column="user_id"/>
        <result property="categoryId" column="category_id"/>
        <result property="viewCount" column="view_count"/>
        <result property="likeCount" column="like_count"/>
        <result property="commentCount" column="comment_count"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <!-- 关联作者信息 -->
        <association property="author" javaType="User">
            <id property="userId" column="author_id"/>
            <result property="username" column="author_username"/>
            <result property="nickname" column="author_nickname"/>
            <result property="avatar" column="author_avatar"/>
        </association>
        <!-- 关联分类信息 -->
        <association property="category" javaType="Category">
            <id property="categoryId" column="cate_id"/>
            <result property="name" column="cate_name"/>
            <result property="icon" column="cate_icon"/>
        </association>
    </resultMap>
    
    <!-- 基础列 -->
    <sql id="baseColumns">
        p.post_id, p.title, p.content, p.user_id, p.category_id, p.view_count, 
        p.like_count, p.comment_count, p.status, p.create_time, p.update_time
    </sql>
    
    <!-- 关联查询列 -->
    <sql id="joinColumns">
        <include refid="baseColumns"/>,
        u.user_id as author_id, u.username as author_username, u.nickname as author_nickname, u.avatar as author_avatar,
        c.category_id as cate_id, c.name as cate_name, c.icon as cate_icon
    </sql>
    
    <!-- 关联查询 -->
    <sql id="joins">
        LEFT JOIN user u ON p.user_id = u.user_id
        LEFT JOIN category c ON p.category_id = c.category_id
    </sql>
    
    <!-- 根据ID获取帖子 -->
    <select id="getPostById" resultMap="postResultMap" parameterType="long">
        SELECT <include refid="joinColumns"/>
        FROM post p
        <include refid="joins"/>
        WHERE p.post_id = #{postId}
    </select>
    
    <!-- 插入新帖子 -->
    <insert id="insertPost" parameterType="Post" useGeneratedKeys="true" keyProperty="postId">
        INSERT INTO post (title, content, user_id, category_id, view_count, like_count, comment_count, status, create_time, update_time)
        VALUES (#{title}, #{content}, #{userId}, #{categoryId}, #{viewCount}, #{likeCount}, #{commentCount}, #{status}, #{createTime}, #{updateTime})
    </insert>
    
    <!-- 更新帖子 -->
    <update id="updatePost" parameterType="Post">
        UPDATE post
        <set>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="status != null">status = #{status},</if>
            update_time = #{updateTime}
        </set>
        WHERE post_id = #{postId} AND user_id = #{userId}
    </update>
    
    <!-- 删除帖子（逻辑删除） -->
    <update id="deletePost" parameterType="long">
        UPDATE post
        SET status = 0, update_time = NOW()
        WHERE post_id = #{postId}
    </update>
    
    <!-- 获取帖子列表（分页） -->
    <select id="getPostList" resultMap="postResultMap">
        SELECT <include refid="joinColumns"/>
        FROM post p
        <include refid="joins"/>
        WHERE p.status > 0
        ORDER BY p.status DESC, p.create_time DESC
        LIMIT #{offset}, #{limit}
    </select>
    
    <!-- 根据分类获取帖子列表（分页） -->
    <select id="getPostListByCategory" resultMap="postResultMap">
        SELECT <include refid="joinColumns"/>
        FROM post p
        <include refid="joins"/>
        WHERE p.status > 0 AND p.category_id = #{categoryId}
        ORDER BY p.status DESC, p.create_time DESC
        LIMIT #{offset}, #{limit}
    </select>
    
    <!-- 根据用户获取帖子列表（分页） -->
    <select id="getPostListByUser" resultMap="postResultMap">
        SELECT <include refid="joinColumns"/>
        FROM post p
        <include refid="joins"/>
        WHERE p.status > 0 AND p.user_id = #{userId}
        ORDER BY p.create_time DESC
        LIMIT #{offset}, #{limit}
    </select>
    
    <!-- 获取帖子总数 -->
    <select id="getPostCount" resultType="int">
        SELECT COUNT(*)
        FROM post
        WHERE status > 0
    </select>
    
    <!-- 获取分类下的帖子总数 -->
    <select id="getPostCountByCategory" resultType="int" parameterType="int">
        SELECT COUNT(*)
        FROM post
        WHERE status > 0 AND category_id = #{categoryId}
    </select>
    
    <!-- 获取用户的帖子总数 -->
    <select id="getPostCountByUser" resultType="int" parameterType="long">
        SELECT COUNT(*)
        FROM post
        WHERE status > 0 AND user_id = #{userId}
    </select>
    
    <!-- 增加浏览量 -->
    <update id="incrementViewCount" parameterType="long">
        UPDATE post
        SET view_count = view_count + 1
        WHERE post_id = #{postId}
    </update>
    
    <!-- 更新点赞数 -->
    <update id="updateLikeCount">
        UPDATE post
        SET like_count = like_count + #{count}
        WHERE post_id = #{postId}
    </update>
    
    <!-- 更新评论数 -->
    <update id="updateCommentCount">
        UPDATE post
        SET comment_count = comment_count + #{count}
        WHERE post_id = #{postId}
    </update>
    
    <!-- 搜索帖子 -->
    <select id="searchPosts" resultMap="postResultMap">
        SELECT <include refid="joinColumns"/>
        FROM post p
        <include refid="joins"/>
        WHERE p.status > 0 AND (p.title LIKE CONCAT('%', #{keyword}, '%') OR p.content LIKE CONCAT('%', #{keyword}, '%'))
        ORDER BY p.create_time DESC
        LIMIT #{offset}, #{limit}
    </select>
    
    <!-- 获取搜索结果总数 -->
    <select id="getSearchCount" resultType="int" parameterType="string">
        SELECT COUNT(*)
        FROM post
        WHERE status > 0 AND (title LIKE CONCAT('%', #{keyword}, '%') OR content LIKE CONCAT('%', #{keyword}, '%'))
    </select>
</mapper> 
package com.forum.controller;

import com.forum.entity.Comment;
import com.forum.service.CommentService;
import com.forum.util.PageResult;
import com.forum.util.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 评论控制器
 */
@RestController
@RequestMapping("/api/comment")
public class CommentController {
    
    @Autowired
    private CommentService commentService;
    
    /**
     * 发表评论
     */
    @PostMapping
    public Result<Comment> addComment(@RequestBody Comment comment, @RequestAttribute("userId") Long userId) {
        comment.setUserId(userId); // 设置当前用户为评论者
        Comment addedComment = commentService.addComment(comment);
        return Result.success("评论成功", addedComment);
    }
    
    /**
     * 更新评论
     */
    @PutMapping("/{commentId}")
    public Result<Comment> updateComment(@PathVariable Long commentId, 
                                       @RequestBody Comment comment,
                                       @RequestAttribute("userId") Long userId) {
        comment.setCommentId(commentId);
        comment.setUserId(userId); // 设置当前用户，用于权限检查
        Comment updatedComment = commentService.updateComment(comment);
        return Result.success("更新成功", updatedComment);
    }
    
    /**
     * 删除评论
     */
    @DeleteMapping("/{commentId}")
    public Result<Boolean> deleteComment(@PathVariable Long commentId, @RequestAttribute("userId") Long userId) {
        boolean result = commentService.deleteComment(commentId, userId);
        return Result.success("删除成功", result);
    }
    
    /**
     * 根据ID获取评论
     */
    @GetMapping("/{commentId}")
    public Result<Comment> getCommentById(@PathVariable Long commentId) {
        Comment comment = commentService.getCommentById(commentId);
        return Result.success(comment);
    }
    
    /**
     * 根据帖子ID获取评论列表（分页）
     */
    @GetMapping("/post/{postId}")
    public Result<PageResult<Comment>> getCommentsByPostId(@PathVariable Long postId,
                                                        @RequestParam(defaultValue = "1") int pageNum,
                                                        @RequestParam(defaultValue = "10") int pageSize) {
        PageResult<Comment> pageResult = commentService.getCommentsByPostId(postId, pageNum, pageSize);
        return Result.success(pageResult);
    }
    
    /**
     * 获取子评论列表
     */
    @GetMapping("/children/{parentId}")
    public Result<List<Comment>> getChildComments(@PathVariable Long parentId) {
        List<Comment> comments = commentService.getChildComments(parentId);
        return Result.success(comments);
    }
    
    /**
     * 根据用户ID获取评论列表（分页）
     */
    @GetMapping("/user/{userId}")
    public Result<PageResult<Comment>> getCommentsByUserId(@PathVariable Long userId,
                                                        @RequestParam(defaultValue = "1") int pageNum,
                                                        @RequestParam(defaultValue = "10") int pageSize) {
        PageResult<Comment> pageResult = commentService.getCommentsByUserId(userId, pageNum, pageSize);
        return Result.success(pageResult);
    }
} 
package com.forum.service.impl;

import com.forum.dao.UserDao;
import com.forum.entity.User;
import com.forum.exception.BusinessException;
import com.forum.exception.NotFoundException;
import com.forum.service.UserService;
import com.forum.util.Constant;
import com.forum.util.PageResult;
import com.forum.util.PasswordUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 用户服务实现类
 */
@Service
public class UserServiceImpl implements UserService {
    
    private static final Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);
    
    @Autowired
    private UserDao userDao;
    
    @Override
    @Transactional
    public User register(User user) {
        // 检查用户名是否已存在
        if (checkUsername(user.getUsername())) {
            throw new BusinessException("用户名已存在");
        }
        
        // 检查邮箱是否已存在
        if (checkEmail(user.getEmail())) {
            throw new BusinessException("邮箱已存在");
        }
        
        // 设置默认值
        Date now = new Date();
        user.setPassword(PasswordUtil.encode(user.getPassword()));
        user.setStatus(Constant.User.STATUS_ENABLED);
        user.setRole(Constant.User.ROLE_USER);
        user.setCreateTime(now);
        user.setUpdateTime(now);
        
        // 如果没有设置昵称，使用用户名作为昵称
        if (user.getNickname() == null || user.getNickname().trim().isEmpty()) {
            user.setNickname(user.getUsername());
        }
        
        // 如果没有设置头像，使用默认头像
        if (user.getAvatar() == null || user.getAvatar().trim().isEmpty()) {
            user.setAvatar("/static/images/avatar/default.jpg");
        }
        
        // 插入数据库
        userDao.insertUser(user);
        
        logger.info("用户注册成功: {}", user.getUsername());
        return user;
    }
    
    @Override
    public User login(String username, String password) {
        User user = userDao.getUserByUsername(username);
        
        // 用户不存在
        if (user == null) {
            throw new BusinessException("用户名或密码错误");
        }
        
        // 账号被禁用
        if (user.getStatus() == Constant.User.STATUS_DISABLED) {
            throw new BusinessException("账号已被禁用，请联系管理员");
        }
        
        // 密码错误
        if (!PasswordUtil.matches(password, user.getPassword())) {
            throw new BusinessException("用户名或密码错误");
        }
        
        // 清除密码
        user.setPassword(null);
        
        logger.info("用户登录成功: {}", username);
        return user;
    }
    
    @Override
    public User getUserById(Long userId) {
        User user = userDao.getUserById(userId);
        if (user == null) {
            throw new NotFoundException("用户不存在");
        }
        
        // 清除密码
        user.setPassword(null);
        return user;
    }
    
    @Override
    public User getUserByUsername(String username) {
        User user = userDao.getUserByUsername(username);
        if (user == null) {
            throw new NotFoundException("用户不存在");
        }
        
        // 清除密码
        user.setPassword(null);
        return user;
    }
    
    @Override
    @Transactional
    public User updateUser(User user) {
        // 检查用户是否存在
        User existingUser = userDao.getUserById(user.getUserId());
        if (existingUser == null) {
            throw new NotFoundException("用户不存在");
        }
        
        // 如果修改了用户名，检查新用户名是否已存在
        if (user.getUsername() != null && !user.getUsername().equals(existingUser.getUsername())) {
            if (checkUsername(user.getUsername())) {
                throw new BusinessException("用户名已存在");
            }
        }
        
        // 如果修改了邮箱，检查新邮箱是否已存在
        if (user.getEmail() != null && !user.getEmail().equals(existingUser.getEmail())) {
            if (checkEmail(user.getEmail())) {
                throw new BusinessException("邮箱已存在");
            }
        }
        
        // 设置更新时间
        user.setUpdateTime(new Date());
        
        // 更新数据库
        userDao.updateUser(user);
        
        // 重新获取用户信息
        User updatedUser = userDao.getUserById(user.getUserId());
        updatedUser.setPassword(null);
        
        logger.info("用户信息更新成功: {}", user.getUserId());
        return updatedUser;
    }
    
    @Override
    @Transactional
    public boolean updatePassword(Long userId, String oldPassword, String newPassword) {
        // 检查用户是否存在
        User user = userDao.getUserById(userId);
        if (user == null) {
            throw new NotFoundException("用户不存在");
        }
        
        // 检查旧密码是否正确
        if (!PasswordUtil.matches(oldPassword, user.getPassword())) {
            throw new BusinessException("旧密码错误");
        }
        
        // 更新密码
        userDao.updatePassword(userId, PasswordUtil.encode(newPassword));
        
        logger.info("用户密码更新成功: {}", userId);
        return true;
    }
    
    @Override
    @Transactional
    public boolean deleteUser(Long userId) {
        // 检查用户是否存在
        User user = userDao.getUserById(userId);
        if (user == null) {
            throw new NotFoundException("用户不存在");
        }
        
        // 删除用户
        userDao.deleteUser(userId);
        
        logger.info("用户删除成功: {}", userId);
        return true;
    }
    
    @Override
    public PageResult<User> getUserList(int pageNum, int pageSize) {
        // 计算偏移量
        int offset = (pageNum - 1) * pageSize;
        
        // 查询用户列表
        List<User> userList = userDao.getUserList(offset, pageSize);
        
        // 查询总数
        int total = userDao.getUserCount();
        
        // 清除密码
        userList.forEach(user -> user.setPassword(null));
        
        return new PageResult<>(pageNum, pageSize, total, userList);
    }
    
    @Override
    public boolean checkUsername(String username) {
        return userDao.getUserByUsername(username) != null;
    }
    
    @Override
    public boolean checkEmail(String email) {
        return userDao.getUserByEmail(email) != null;
    }
} 
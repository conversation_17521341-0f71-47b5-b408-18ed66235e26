package com.forum.controller;

import com.forum.entity.Notification;
import com.forum.service.NotificationService;
import com.forum.util.PageResult;
import com.forum.util.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 通知控制器
 */
@RestController
@RequestMapping("/api/notification")
public class NotificationController {
    
    @Autowired
    private NotificationService notificationService;
    
    /**
     * 获取用户通知列表（分页）
     */
    @GetMapping("/list")
    public Result<PageResult<Notification>> getNotifications(@RequestAttribute("userId") Long userId,
                                                          @RequestParam(defaultValue = "1") int pageNum,
                                                          @RequestParam(defaultValue = "10") int pageSize) {
        PageResult<Notification> pageResult = notificationService.getNotifications(userId, pageNum, pageSize);
        return Result.success(pageResult);
    }
    
    /**
     * 获取用户未读通知列表
     */
    @GetMapping("/unread")
    public Result<List<Notification>> getUnreadNotifications(@RequestAttribute("userId") Long userId) {
        List<Notification> notifications = notificationService.getUnreadNotifications(userId);
        return Result.success(notifications);
    }
    
    /**
     * 获取用户未读通知数
     */
    @GetMapping("/unread/count")
    public Result<Integer> getUnreadNotificationCount(@RequestAttribute("userId") Long userId) {
        int count = notificationService.getUnreadNotificationCount(userId);
        return Result.success(count);
    }
    
    /**
     * 将通知标记为已读
     */
    @PutMapping("/{notificationId}/read")
    public Result<Boolean> markAsRead(@PathVariable Long notificationId) {
        boolean result = notificationService.markAsRead(notificationId);
        return Result.success("标记成功", result);
    }
    
    /**
     * 将所有通知标记为已读
     */
    @PutMapping("/read/all")
    public Result<Boolean> markAllAsRead(@RequestAttribute("userId") Long userId) {
        boolean result = notificationService.markAllAsRead(userId);
        return Result.success("标记成功", result);
    }
    
    /**
     * 删除通知
     */
    @DeleteMapping("/{notificationId}")
    public Result<Boolean> deleteNotification(@PathVariable Long notificationId) {
        boolean result = notificationService.deleteNotification(notificationId);
        return Result.success("删除成功", result);
    }
    
    /**
     * 批量删除通知
     */
    @DeleteMapping("/batch")
    public Result<Boolean> batchDeleteNotifications(@RequestAttribute("userId") Long userId,
                                                 @RequestBody List<Long> notificationIds) {
        boolean result = notificationService.batchDeleteNotifications(userId, notificationIds);
        return Result.success("删除成功", result);
    }
} 
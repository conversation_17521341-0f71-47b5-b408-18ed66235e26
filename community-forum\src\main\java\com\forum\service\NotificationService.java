package com.forum.service;

import com.forum.entity.Notification;
import com.forum.util.PageResult;

import java.util.List;

/**
 * 通知服务接口
 */
public interface NotificationService {
    
    /**
     * 发送评论通知
     * 
     * @param senderId 发送者ID
     * @param receiverId 接收者ID
     * @param postId 帖子ID
     * @param commentId 评论ID
     */
    void sendCommentNotification(Long senderId, Long receiverId, Long postId, Long commentId);
    
    /**
     * 发送点赞通知
     * 
     * @param senderId 发送者ID
     * @param receiverId 接收者ID
     * @param postId 帖子ID
     */
    void sendLikeNotification(Long senderId, Long receiverId, Long postId);
    
    /**
     * 发送收藏通知
     * 
     * @param senderId 发送者ID
     * @param receiverId 接收者ID
     * @param postId 帖子ID
     */
    void sendFavoriteNotification(Long senderId, Long receiverId, Long postId);
    
    /**
     * 发送系统通知
     * 
     * @param receiverId 接收者ID
     * @param content 通知内容
     */
    void sendSystemNotification(Long receiverId, String content);
    
    /**
     * 获取用户通知列表
     * 
     * @param receiverId 接收者ID
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 通知分页列表
     */
    PageResult<Notification> getNotifications(Long receiverId, int pageNum, int pageSize);
    
    /**
     * 获取用户未读通知列表
     * 
     * @param receiverId 接收者ID
     * @return 未读通知列表
     */
    List<Notification> getUnreadNotifications(Long receiverId);
    
    /**
     * 获取用户未读通知数
     * 
     * @param receiverId 接收者ID
     * @return 未读通知数
     */
    int getUnreadNotificationCount(Long receiverId);
    
    /**
     * 将通知标记为已读
     * 
     * @param notificationId 通知ID
     * @return 是否成功
     */
    boolean markAsRead(Long notificationId);
    
    /**
     * 将所有通知标记为已读
     * 
     * @param receiverId 接收者ID
     * @return 是否成功
     */
    boolean markAllAsRead(Long receiverId);
    
    /**
     * 删除通知
     * 
     * @param notificationId 通知ID
     * @return 是否成功
     */
    boolean deleteNotification(Long notificationId);
    
    /**
     * 批量删除通知
     * 
     * @param receiverId 接收者ID
     * @param notificationIds 通知ID列表
     * @return 是否成功
     */
    boolean batchDeleteNotifications(Long receiverId, List<Long> notificationIds);
} 
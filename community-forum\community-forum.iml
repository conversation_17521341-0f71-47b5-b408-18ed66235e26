<?xml version="1.0" encoding="UTF-8"?>
<module org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet type="web" name="Web">
      <configuration>
        <descriptors>
          <deploymentDescriptor name="web.xml" url="file://$MODULE_DIR$/src/main/webapp/WEB-INF/web.xml" />
        </descriptors>
        <webroots>
          <root url="file://$MODULE_DIR$/src/main/webapp" relative="/" />
        </webroots>
      </configuration>
    </facet>
    <facet type="Spring" name="Spring">
      <configuration />
    </facet>
  </component>
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_8">
    <output url="file://$MODULE_DIR$/target/classes" />
    <output-test url="file://$MODULE_DIR$/target/test-classes" />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/resources" type="java-resource" />
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Maven: org.springframework:spring-webmvc:5.3.20" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-aop:5.3.20" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-beans:5.3.20" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context:5.3.20" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-core:5.3.20" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jcl:5.3.20" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-expression:5.3.20" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-web:5.3.20" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jdbc:5.3.20" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-tx:5.3.20" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-aspects:5.3.20" level="project" />
    <orderEntry type="library" name="Maven: org.aspectj:aspectjweaver:1.9.7" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context-support:5.3.20" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-web:5.6.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-core:5.6.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-config:5.6.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-crypto:5.6.3" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis:mybatis:3.5.9" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis:mybatis-spring:2.0.7" level="project" />
    <orderEntry type="library" name="Maven: mysql:mysql-connector-java:8.0.28" level="project" />
    <orderEntry type="library" name="Maven: com.google.protobuf:protobuf-java:3.11.4" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:druid:1.2.9" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-databind:2.13.3" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-annotations:2.13.3" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-core:2.13.3" level="project" />
    <orderEntry type="library" scope="PROVIDED" name="Maven: javax.servlet:javax.servlet-api:4.0.1" level="project" />
    <orderEntry type="library" scope="PROVIDED" name="Maven: javax.servlet.jsp:javax.servlet.jsp-api:2.3.3" level="project" />
    <orderEntry type="library" name="Maven: jstl:jstl:1.2" level="project" />
    <orderEntry type="library" name="Maven: commons-fileupload:commons-fileupload:1.4" level="project" />
    <orderEntry type="library" name="Maven: commons-io:commons-io:2.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-redis:2.6.4" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-keyvalue:2.6.4" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-commons:2.6.4" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-oxm:5.3.19" level="project" />
    <orderEntry type="library" name="Maven: redis.clients:jedis:3.8.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-pool2:2.11.1" level="project" />
    <orderEntry type="library" scope="PROVIDED" name="Maven: org.projectlombok:lombok:1.18.24" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-core:2.17.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-api:2.17.2" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:slf4j-api:1.7.36" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-slf4j-impl:2.17.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-websocket:5.3.20" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-messaging:5.3.20" level="project" />
    <orderEntry type="library" name="Maven: io.jsonwebtoken:jjwt:0.9.1" level="project" />
    <orderEntry type="library" name="Maven: com.google.code.gson:gson:2.9.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: junit:junit:4.13.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.hamcrest:hamcrest-core:1.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework:spring-test:5.3.20" level="project" />
  </component>
</module>
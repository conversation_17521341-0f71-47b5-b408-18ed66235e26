package com.forum.dao;

import com.forum.entity.User;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * User DAO interface
 */
public interface UserDao {
    
    /**
     * Get user by ID
     * 
     * @param userId User ID
     * @return User object
     */
    User getUserById(Long userId);
    
    /**
     * Get user by username
     * 
     * @param username Username
     * @return User object
     */
    User getUserByUsername(String username);
    
    /**
     * Get user by email
     * 
     * @param email Email
     * @return User object
     */
    User getUserByEmail(String email);
    
    /**
     * Insert a new user
     * 
     * @param user User object
     * @return Number of rows affected
     */
    int insertUser(User user);
    
    /**
     * Update user information
     * 
     * @param user User object
     * @return Number of rows affected
     */
    int updateUser(User user);
    
    /**
     * Update user password
     * 
     * @param userId User ID
     * @param password New password (encrypted)
     * @return Number of rows affected
     */
    int updatePassword(@Param("userId") Long userId, @Param("password") String password);
    
    /**
     * Delete user
     * 
     * @param userId User ID
     * @return Number of rows affected
     */
    int deleteUser(Long userId);
    
    /**
     * Get user list with pagination
     * 
     * @param offset Offset
     * @param limit Limit
     * @return User list
     */
    List<User> getUserList(@Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * Get total user count
     * 
     * @return Total user count
     */
    int getUserCount();
} 
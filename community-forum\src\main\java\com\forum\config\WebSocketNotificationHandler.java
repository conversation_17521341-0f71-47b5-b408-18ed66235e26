package com.forum.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * WebSocket通知处理器
 */
public class WebSocketNotificationHandler extends TextWebSocketHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(WebSocketNotificationHandler.class);
    
    // 存储用户WebSocket会话，key为用户ID
    private static final Map<Long, WebSocketSession> userSessions = new ConcurrentHashMap<>();
    
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        // 从session中获取用户ID
        Long userId = getUserIdFromSession(session);
        if (userId != null) {
            // 存储会话
            userSessions.put(userId, session);
            logger.info("用户{}建立WebSocket连接", userId);
        }
    }
    
    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        // 处理接收到的消息，一般客户端不会发送消息，这里只是记录日志
        logger.debug("收到WebSocket消息: {}", message.getPayload());
    }
    
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        // 连接关闭时，移除会话
        Long userId = getUserIdFromSession(session);
        if (userId != null) {
            userSessions.remove(userId);
            logger.info("用户{}关闭WebSocket连接", userId);
        }
    }
    
    /**
     * 发送通知给指定用户
     * 
     * @param userId 用户ID
     * @param message 消息内容
     */
    public static void sendNotification(Long userId, String message) {
        WebSocketSession session = userSessions.get(userId);
        if (session != null && session.isOpen()) {
            try {
                session.sendMessage(new TextMessage(message));
                logger.debug("发送WebSocket消息到用户{}: {}", userId, message);
            } catch (IOException e) {
                logger.error("发送WebSocket消息失败", e);
            }
        }
    }
    
    /**
     * 从WebSocket会话中获取用户ID
     */
    private Long getUserIdFromSession(WebSocketSession session) {
        Object userId = session.getAttributes().get("userId");
        return userId != null ? (Long) userId : null;
    }
} 
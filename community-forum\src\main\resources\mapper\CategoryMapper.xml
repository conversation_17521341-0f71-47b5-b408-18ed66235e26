<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.forum.dao.CategoryDao">
    <!-- 结果映射 -->
    <resultMap id="categoryResultMap" type="Category">
        <id property="categoryId" column="category_id"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="icon" column="icon"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    
    <!-- 基础列 -->
    <sql id="baseColumns">
        category_id, name, description, icon, sort_order, status, create_time, update_time
    </sql>
    
    <!-- 根据ID获取分类 -->
    <select id="getCategoryById" resultMap="categoryResultMap" parameterType="int">
        SELECT <include refid="baseColumns"/>
        FROM category
        WHERE category_id = #{categoryId}
    </select>
    
    <!-- 根据名称获取分类 -->
    <select id="getCategoryByName" resultMap="categoryResultMap" parameterType="string">
        SELECT <include refid="baseColumns"/>
        FROM category
        WHERE name = #{name}
    </select>
    
    <!-- 插入新分类 -->
    <insert id="insertCategory" parameterType="Category" useGeneratedKeys="true" keyProperty="categoryId">
        INSERT INTO category (name, description, icon, sort_order, status, create_time, update_time)
        VALUES (#{name}, #{description}, #{icon}, #{sortOrder}, #{status}, #{createTime}, #{updateTime})
    </insert>
    
    <!-- 更新分类 -->
    <update id="updateCategory" parameterType="Category">
        UPDATE category
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            update_time = #{updateTime}
        </set>
        WHERE category_id = #{categoryId}
    </update>
    
    <!-- 删除分类 -->
    <delete id="deleteCategory" parameterType="int">
        DELETE FROM category
        WHERE category_id = #{categoryId}
    </delete>
    
    <!-- 获取所有分类 -->
    <select id="getAllCategories" resultMap="categoryResultMap">
        SELECT <include refid="baseColumns"/>
        FROM category
        ORDER BY sort_order ASC, create_time ASC
    </select>
    
    <!-- 获取启用的分类 -->
    <select id="getEnabledCategories" resultMap="categoryResultMap">
        SELECT <include refid="baseColumns"/>
        FROM category
        WHERE status = 1
        ORDER BY sort_order ASC, create_time ASC
    </select>
    
    <!-- 获取分类列表（分页） -->
    <select id="getCategoryList" resultMap="categoryResultMap">
        SELECT <include refid="baseColumns"/>
        FROM category
        ORDER BY sort_order ASC, create_time ASC
        LIMIT #{offset}, #{limit}
    </select>
    
    <!-- 获取分类总数 -->
    <select id="getCategoryCount" resultType="int">
        SELECT COUNT(*)
        FROM category
    </select>
</mapper> 
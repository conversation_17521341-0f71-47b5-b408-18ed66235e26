package com.forum.service;

import com.forum.entity.Comment;
import com.forum.util.PageResult;

import java.util.List;

/**
 * 评论服务接口
 */
public interface CommentService {
    
    /**
     * 发表评论
     * 
     * @param comment 评论信息
     * @return 发表成功的评论
     */
    Comment addComment(Comment comment);
    
    /**
     * 更新评论
     * 
     * @param comment 评论信息
     * @return 更新后的评论
     */
    Comment updateComment(Comment comment);
    
    /**
     * 删除评论
     * 
     * @param commentId 评论ID
     * @param userId 用户ID（用于权限检查）
     * @return 是否成功
     */
    boolean deleteComment(Long commentId, Long userId);
    
    /**
     * 根据ID获取评论
     * 
     * @param commentId 评论ID
     * @return 评论信息
     */
    Comment getCommentById(Long commentId);
    
    /**
     * 根据帖子ID获取评论列表（分页）
     * 
     * @param postId 帖子ID
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 评论分页列表
     */
    PageResult<Comment> getCommentsByPostId(Long postId, int pageNum, int pageSize);
    
    /**
     * 获取子评论列表
     * 
     * @param parentId 父评论ID
     * @return 子评论列表
     */
    List<Comment> getChildComments(Long parentId);
    
    /**
     * 根据用户ID获取评论列表（分页）
     * 
     * @param userId 用户ID
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 评论分页列表
     */
    PageResult<Comment> getCommentsByUserId(Long userId, int pageNum, int pageSize);
} 
package com.forum.util;

/**
 * 常量类
 */
public class Constant {
    
    /**
     * 用户相关常量
     */
    public static class User {
        /**
         * 用户状态：禁用
         */
        public static final int STATUS_DISABLED = 0;
        
        /**
         * 用户状态：启用
         */
        public static final int STATUS_ENABLED = 1;
        
        /**
         * 用户角色：普通用户
         */
        public static final String ROLE_USER = "USER";
        
        /**
         * 用户角色：管理员
         */
        public static final String ROLE_ADMIN = "ADMIN";
    }
    
    /**
     * 帖子相关常量
     */
    public static class Post {
        /**
         * 帖子状态：删除
         */
        public static final int STATUS_DELETED = 0;
        
        /**
         * 帖子状态：正常
         */
        public static final int STATUS_NORMAL = 1;
        
        /**
         * 帖子状态：置顶
         */
        public static final int STATUS_PINNED = 2;
    }
    
    /**
     * 评论相关常量
     */
    public static class Comment {
        /**
         * 评论状态：删除
         */
        public static final int STATUS_DELETED = 0;
        
        /**
         * 评论状态：正常
         */
        public static final int STATUS_NORMAL = 1;
    }
    
    /**
     * 分类相关常量
     */
    public static class Category {
        /**
         * 分类状态：禁用
         */
        public static final int STATUS_DISABLED = 0;
        
        /**
         * 分类状态：启用
         */
        public static final int STATUS_ENABLED = 1;
    }
    
    /**
     * 通知相关常量
     */
    public static class Notification {
        /**
         * 通知类型：评论
         */
        public static final String TYPE_COMMENT = "COMMENT";
        
        /**
         * 通知类型：点赞
         */
        public static final String TYPE_LIKE = "LIKE";
        
        /**
         * 通知类型：收藏
         */
        public static final String TYPE_FAVORITE = "FAVORITE";
        
        /**
         * 通知类型：系统
         */
        public static final String TYPE_SYSTEM = "SYSTEM";
        
        /**
         * 通知状态：未读
         */
        public static final int STATUS_UNREAD = 0;
        
        /**
         * 通知状态：已读
         */
        public static final int STATUS_READ = 1;
    }
    
    /**
     * Redis相关常量
     */
    public static class Redis {
        /**
         * 用户信息缓存前缀
         */
        public static final String PREFIX_USER = "forum:user:";
        
        /**
         * 帖子信息缓存前缀
         */
        public static final String PREFIX_POST = "forum:post:";
        
        /**
         * 分类信息缓存前缀
         */
        public static final String PREFIX_CATEGORY = "forum:category:";
        
        /**
         * 用户Token前缀
         */
        public static final String PREFIX_TOKEN = "forum:token:";
        
        /**
         * 缓存过期时间（秒）
         */
        public static final int EXPIRE_TIME = 3600; // 1小时
    }
} 
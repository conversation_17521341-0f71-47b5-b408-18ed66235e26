package com.forum.dao;

import com.forum.entity.PostLike;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * PostLike DAO interface
 */
public interface PostLikeDao {
    
    /**
     * Get post like by post ID and user ID
     * 
     * @param postId Post ID
     * @param userId User ID
     * @return PostLike object
     */
    PostLike getPostLike(@Param("postId") Long postId, @Param("userId") Long userId);
    
    /**
     * Insert a new post like
     * 
     * @param postLike PostLike object
     * @return Number of rows affected
     */
    int insertPostLike(PostLike postLike);
    
    /**
     * Delete post like
     * 
     * @param postId Post ID
     * @param userId User ID
     * @return Number of rows affected
     */
    int deletePostLike(@Param("postId") Long postId, @Param("userId") Long userId);
    
    /**
     * Get post likes by post ID
     * 
     * @param postId Post ID
     * @return PostLike list
     */
    List<PostLike> getPostLikesByPostId(Long postId);
    
    /**
     * Get post likes by user ID
     * 
     * @param userId User ID
     * @param offset Offset
     * @param limit Limit
     * @return PostLike list
     */
    List<PostLike> getPostLikesByUserId(@Param("userId") Long userId, 
                                       @Param("offset") int offset, 
                                       @Param("limit") int limit);
    
    /**
     * Get post like count by post ID
     * 
     * @param postId Post ID
     * @return Post like count
     */
    int getPostLikeCountByPostId(Long postId);
    
    /**
     * Get post like count by user ID
     * 
     * @param userId User ID
     * @return Post like count
     */
    int getPostLikeCountByUserId(Long userId);
} 
package com.forum.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Post Favorite entity
 */
@Data
public class PostFavorite implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * Post ID
     */
    private Long postId;

    /**
     * User ID
     */
    private Long userId;

    /**
     * Create time
     */
    private Date createTime;
} 
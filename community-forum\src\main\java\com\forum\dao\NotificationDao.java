package com.forum.dao;

import com.forum.entity.Notification;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 通知DAO接口
 */
public interface NotificationDao {
    
    /**
     * 根据ID获取通知
     * 
     * @param notificationId 通知ID
     * @return 通知对象
     */
    Notification getNotificationById(Long notificationId);
    
    /**
     * 插入新通知
     * 
     * @param notification 通知对象
     * @return 影响行数
     */
    int insertNotification(Notification notification);
    
    /**
     * 更新通知
     * 
     * @param notification 通知对象
     * @return 影响行数
     */
    int updateNotification(Notification notification);
    
    /**
     * 删除通知
     * 
     * @param notificationId 通知ID
     * @return 影响行数
     */
    int deleteNotification(Long notificationId);
    
    /**
     * 批量删除通知
     * 
     * @param receiverId 接收者ID
     * @param notificationIds 通知ID列表
     * @return 影响行数
     */
    int batchDeleteNotifications(@Param("receiverId") Long receiverId, @Param("notificationIds") List<Long> notificationIds);
    
    /**
     * 获取用户的通知列表（分页）
     * 
     * @param receiverId 接收者ID
     * @param offset 偏移量
     * @param limit 限制数
     * @return 通知列表
     */
    List<Notification> getNotificationsByReceiverId(@Param("receiverId") Long receiverId, 
                                                  @Param("offset") int offset, 
                                                  @Param("limit") int limit);
    
    /**
     * 获取用户未读通知列表
     * 
     * @param receiverId 接收者ID
     * @return 通知列表
     */
    List<Notification> getUnreadNotifications(Long receiverId);
    
    /**
     * 获取用户通知总数
     * 
     * @param receiverId 接收者ID
     * @return 通知总数
     */
    int getNotificationCountByReceiverId(Long receiverId);
    
    /**
     * 获取用户未读通知数
     * 
     * @param receiverId 接收者ID
     * @return 未读通知数
     */
    int getUnreadNotificationCount(Long receiverId);
    
    /**
     * 将通知标记为已读
     * 
     * @param notificationId 通知ID
     * @return 影响行数
     */
    int markAsRead(Long notificationId);
    
    /**
     * 将所有通知标记为已读
     * 
     * @param receiverId 接收者ID
     * @return 影响行数
     */
    int markAllAsRead(Long receiverId);
} 
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.forum.dao.NotificationDao">
    <!-- 结果映射 -->
    <resultMap id="notificationResultMap" type="Notification">
        <id property="notificationId" column="notification_id"/>
        <result property="type" column="type"/>
        <result property="senderId" column="sender_id"/>
        <result property="receiverId" column="receiver_id"/>
        <result property="postId" column="post_id"/>
        <result property="commentId" column="comment_id"/>
        <result property="content" column="content"/>
        <result property="isRead" column="is_read"/>
        <result property="createTime" column="create_time"/>
        <!-- 关联发送者信息 -->
        <association property="sender" javaType="User">
            <id property="userId" column="sender_user_id"/>
            <result property="username" column="sender_username"/>
            <result property="nickname" column="sender_nickname"/>
            <result property="avatar" column="sender_avatar"/>
        </association>
        <!-- 关联帖子信息 -->
        <association property="post" javaType="Post">
            <id property="postId" column="post_post_id"/>
            <result property="title" column="post_title"/>
        </association>
    </resultMap>
    
    <!-- 基础列 -->
    <sql id="baseColumns">
        n.notification_id, n.type, n.sender_id, n.receiver_id, n.post_id, n.comment_id, n.content, n.is_read, n.create_time
    </sql>
    
    <!-- 关联查询列 -->
    <sql id="joinColumns">
        <include refid="baseColumns"/>,
        u.user_id as sender_user_id, u.username as sender_username, u.nickname as sender_nickname, u.avatar as sender_avatar,
        p.post_id as post_post_id, p.title as post_title
    </sql>
    
    <!-- 关联查询 -->
    <sql id="joins">
        LEFT JOIN user u ON n.sender_id = u.user_id
        LEFT JOIN post p ON n.post_id = p.post_id
    </sql>
    
    <!-- 根据ID获取通知 -->
    <select id="getNotificationById" resultMap="notificationResultMap" parameterType="long">
        SELECT <include refid="joinColumns"/>
        FROM notification n
        <include refid="joins"/>
        WHERE n.notification_id = #{notificationId}
    </select>
    
    <!-- 插入新通知 -->
    <insert id="insertNotification" parameterType="Notification" useGeneratedKeys="true" keyProperty="notificationId">
        INSERT INTO notification (type, sender_id, receiver_id, post_id, comment_id, content, is_read, create_time)
        VALUES (#{type}, #{senderId}, #{receiverId}, #{postId}, #{commentId}, #{content}, #{isRead}, #{createTime})
    </insert>
    
    <!-- 更新通知 -->
    <update id="updateNotification" parameterType="Notification">
        UPDATE notification
        <set>
            <if test="content != null">content = #{content},</if>
            <if test="isRead != null">is_read = #{isRead},</if>
        </set>
        WHERE notification_id = #{notificationId}
    </update>
    
    <!-- 删除通知 -->
    <delete id="deleteNotification" parameterType="long">
        DELETE FROM notification
        WHERE notification_id = #{notificationId}
    </delete>
    
    <!-- 批量删除通知 -->
    <delete id="batchDeleteNotifications">
        DELETE FROM notification
        WHERE receiver_id = #{receiverId} AND notification_id IN
        <foreach collection="notificationIds" item="notificationId" open="(" separator="," close=")">
            #{notificationId}
        </foreach>
    </delete>
    
    <!-- 根据接收者ID获取通知列表（分页） -->
    <select id="getNotificationsByReceiverId" resultMap="notificationResultMap">
        SELECT <include refid="joinColumns"/>
        FROM notification n
        <include refid="joins"/>
        WHERE n.receiver_id = #{receiverId}
        ORDER BY n.create_time DESC
        LIMIT #{offset}, #{limit}
    </select>
    
    <!-- 获取用户未读通知列表 -->
    <select id="getUnreadNotifications" resultMap="notificationResultMap" parameterType="long">
        SELECT <include refid="joinColumns"/>
        FROM notification n
        <include refid="joins"/>
        WHERE n.receiver_id = #{receiverId} AND n.is_read = 0
        ORDER BY n.create_time DESC
    </select>
    
    <!-- 获取用户通知总数 -->
    <select id="getNotificationCountByReceiverId" resultType="int" parameterType="long">
        SELECT COUNT(*)
        FROM notification
        WHERE receiver_id = #{receiverId}
    </select>
    
    <!-- 获取用户未读通知数 -->
    <select id="getUnreadNotificationCount" resultType="int" parameterType="long">
        SELECT COUNT(*)
        FROM notification
        WHERE receiver_id = #{receiverId} AND is_read = 0
    </select>
    
    <!-- 将通知标记为已读 -->
    <update id="markAsRead" parameterType="long">
        UPDATE notification
        SET is_read = 1
        WHERE notification_id = #{notificationId}
    </update>
    
    <!-- 将所有通知标记为已读 -->
    <update id="markAllAsRead" parameterType="long">
        UPDATE notification
        SET is_read = 1
        WHERE receiver_id = #{receiverId} AND is_read = 0
    </update>
</mapper> 
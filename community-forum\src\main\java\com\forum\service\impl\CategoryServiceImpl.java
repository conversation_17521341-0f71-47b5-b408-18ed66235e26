package com.forum.service.impl;

import com.forum.dao.CategoryDao;
import com.forum.entity.Category;
import com.forum.exception.BusinessException;
import com.forum.exception.NotFoundException;
import com.forum.service.CategoryService;
import com.forum.util.Constant;
import com.forum.util.PageResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 分类服务实现类
 */
@Service
public class CategoryServiceImpl implements CategoryService {
    
    private static final Logger logger = LoggerFactory.getLogger(CategoryServiceImpl.class);
    
    @Autowired
    private CategoryDao categoryDao;
    
    @Override
    @Transactional
    public Category addCategory(Category category) {
        // 检查分类名称是否已存在
        if (checkCategoryName(category.getName())) {
            throw new BusinessException("分类名称已存在");
        }
        
        // 设置默认值
        Date now = new Date();
        category.setStatus(Constant.Category.STATUS_ENABLED);
        category.setCreateTime(now);
        category.setUpdateTime(now);
        
        // 如果没有设置图标，使用默认图标
        if (category.getIcon() == null || category.getIcon().trim().isEmpty()) {
            category.setIcon("fa-folder");
        }
        
        // 如果没有设置排序，默认为0
        if (category.getSortOrder() == null) {
            category.setSortOrder(0);
        }
        
        // 插入数据库
        categoryDao.insertCategory(category);
        
        logger.info("分类添加成功: {}", category.getName());
        return category;
    }
    
    @Override
    @Transactional
    public Category updateCategory(Category category) {
        // 检查分类是否存在
        Category existingCategory = categoryDao.getCategoryById(category.getCategoryId());
        if (existingCategory == null) {
            throw new NotFoundException("分类不存在");
        }
        
        // 如果修改了名称，检查新名称是否已存在
        if (category.getName() != null && !category.getName().equals(existingCategory.getName())) {
            if (checkCategoryName(category.getName())) {
                throw new BusinessException("分类名称已存在");
            }
        }
        
        // 设置更新时间
        category.setUpdateTime(new Date());
        
        // 更新数据库
        categoryDao.updateCategory(category);
        
        // 重新获取分类信息
        Category updatedCategory = categoryDao.getCategoryById(category.getCategoryId());
        
        logger.info("分类更新成功: {}", category.getCategoryId());
        return updatedCategory;
    }
    
    @Override
    @Transactional
    public boolean deleteCategory(Integer categoryId) {
        // 检查分类是否存在
        Category category = categoryDao.getCategoryById(categoryId);
        if (category == null) {
            throw new NotFoundException("分类不存在");
        }
        
        // 删除分类
        categoryDao.deleteCategory(categoryId);
        
        logger.info("分类删除成功: {}", categoryId);
        return true;
    }
    
    @Override
    public Category getCategoryById(Integer categoryId) {
        Category category = categoryDao.getCategoryById(categoryId);
        if (category == null) {
            throw new NotFoundException("分类不存在");
        }
        return category;
    }
    
    @Override
    public List<Category> getAllCategories() {
        return categoryDao.getAllCategories();
    }
    
    @Override
    public List<Category> getEnabledCategories() {
        return categoryDao.getEnabledCategories();
    }
    
    @Override
    public PageResult<Category> getCategoryList(int pageNum, int pageSize) {
        // 计算偏移量
        int offset = (pageNum - 1) * pageSize;
        
        // 查询分类列表
        List<Category> categoryList = categoryDao.getCategoryList(offset, pageSize);
        
        // 查询总数
        int total = categoryDao.getCategoryCount();
        
        return new PageResult<>(pageNum, pageSize, total, categoryList);
    }
    
    @Override
    public boolean checkCategoryName(String name) {
        return categoryDao.getCategoryByName(name) != null;
    }
} 
This directory contains Jansi native libraries extracted from Jansi JAR.

You can add your own build for platforms not natively supported by Jan<PERSON>.
See here [1] on how to compile for your platform and and here [2] how libraries
follow <PERSON><PERSON>'s directory and filename conventions.

[1] https://github.com/fusesource/jansi/tree/master/src/main/native
[2] https://github.com/fusesource/jansi/blob/321a8ff71c731e10f4ea05c607860180276b2215/src/main/java/org/fusesource/jansi/internal/OSInfo.java

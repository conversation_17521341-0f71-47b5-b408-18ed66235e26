package com.forum.controller;

import com.forum.entity.User;
import com.forum.service.UserService;
import com.forum.util.JwtUtil;
import com.forum.util.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/api/user")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    /**
     * 用户注册
     */
    @PostMapping("/register")
    public Result<User> register(@RequestBody User user) {
        User registeredUser = userService.register(user);
        return Result.success("注册成功", registeredUser);
    }
    
    /**
     * 用户登录
     */
    @PostMapping("/login")
    public Result<Map<String, Object>> login(@RequestBody Map<String, String> loginForm, HttpServletResponse response) {
        String username = loginForm.get("username");
        String password = loginForm.get("password");
        
        User user = userService.login(username, password);
        
        // 生成JWT令牌
        String token = JwtUtil.generateToken(user.getUserId());
        
        // 将令牌设置到响应头
        response.setHeader("Authorization", "Bearer " + token);
        
        // 返回用户信息和令牌
        Map<String, Object> result = new HashMap<>();
        result.put("user", user);
        result.put("token", token);
        
        return Result.success("登录成功", result);
    }
    
    /**
     * 获取当前用户信息
     */
    @GetMapping("/info")
    public Result<User> getUserInfo(@RequestAttribute("userId") Long userId) {
        User user = userService.getUserById(userId);
        return Result.success(user);
    }
    
    /**
     * 更新用户信息
     */
    @PutMapping("/info")
    public Result<User> updateUserInfo(@RequestAttribute("userId") Long userId, @RequestBody User user) {
        user.setUserId(userId); // 确保是当前用户
        User updatedUser = userService.updateUser(user);
        return Result.success("更新成功", updatedUser);
    }
    
    /**
     * 修改密码
     */
    @PutMapping("/password")
    public Result<Boolean> updatePassword(@RequestAttribute("userId") Long userId, @RequestBody Map<String, String> passwordForm) {
        String oldPassword = passwordForm.get("oldPassword");
        String newPassword = passwordForm.get("newPassword");
        
        boolean result = userService.updatePassword(userId, oldPassword, newPassword);
        return Result.success("密码修改成功", result);
    }
    
    /**
     * 检查用户名是否存在
     */
    @GetMapping("/check-username")
    public Result<Boolean> checkUsername(@RequestParam String username) {
        boolean exists = userService.checkUsername(username);
        return Result.success(exists);
    }
    
    /**
     * 检查邮箱是否存在
     */
    @GetMapping("/check-email")
    public Result<Boolean> checkEmail(@RequestParam String email) {
        boolean exists = userService.checkEmail(email);
        return Result.success(exists);
    }
} 
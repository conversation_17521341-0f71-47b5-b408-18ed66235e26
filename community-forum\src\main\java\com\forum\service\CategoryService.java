package com.forum.service;

import com.forum.entity.Category;
import com.forum.util.PageResult;

import java.util.List;

/**
 * 分类服务接口
 */
public interface CategoryService {
    
    /**
     * 添加分类
     * 
     * @param category 分类信息
     * @return 添加成功的分类
     */
    Category addCategory(Category category);
    
    /**
     * 更新分类
     * 
     * @param category 分类信息
     * @return 更新后的分类
     */
    Category updateCategory(Category category);
    
    /**
     * 删除分类
     * 
     * @param categoryId 分类ID
     * @return 是否成功
     */
    boolean deleteCategory(Integer categoryId);
    
    /**
     * 根据ID获取分类
     * 
     * @param categoryId 分类ID
     * @return 分类信息
     */
    Category getCategoryById(Integer categoryId);
    
    /**
     * 获取所有分类
     * 
     * @return 分类列表
     */
    List<Category> getAllCategories();
    
    /**
     * 获取启用的分类
     * 
     * @return 分类列表
     */
    List<Category> getEnabledCategories();
    
    /**
     * 获取分类列表（分页）
     * 
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 分类分页列表
     */
    PageResult<Category> getCategoryList(int pageNum, int pageSize);
    
    /**
     * 检查分类名称是否存在
     * 
     * @param name 分类名称
     * @return 是否存在
     */
    boolean checkCategoryName(String name);
} 
package com.forum.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Notification entity
 */
@Data
public class Notification implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * Notification ID
     */
    private Long notificationId;

    /**
     * Notification type: COMMENT, LIKE, FAVORITE, SYSTEM
     */
    private String type;

    /**
     * Sender user ID, NULL if system notification
     */
    private Long senderId;

    /**
     * Receiver user ID
     */
    private Long receiverId;

    /**
     * Related post ID
     */
    private Long postId;

    /**
     * Related comment ID
     */
    private Long commentId;

    /**
     * Notification content
     */
    private String content;

    /**
     * Read status: 0-unread, 1-read
     */
    private Integer isRead;

    /**
     * Create time
     */
    private Date createTime;

    /**
     * Sender information (not stored in database)
     */
    private User sender;

    /**
     * Post information (not stored in database)
     */
    private Post post;
} 
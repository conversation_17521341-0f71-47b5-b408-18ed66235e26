<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.forum.dao.PostLikeDao">
    <!-- 结果映射 -->
    <resultMap id="postLikeResultMap" type="PostLike">
        <id property="id" column="id"/>
        <result property="postId" column="post_id"/>
        <result property="userId" column="user_id"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    
    <!-- 根据帖子ID和用户ID获取点赞记录 -->
    <select id="getPostLike" resultMap="postLikeResultMap">
        SELECT id, post_id, user_id, create_time
        FROM post_like
        WHERE post_id = #{postId} AND user_id = #{userId}
    </select>
    
    <!-- 插入新点赞记录 -->
    <insert id="insertPostLike" parameterType="PostLike" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO post_like (post_id, user_id, create_time)
        VALUES (#{postId}, #{userId}, #{createTime})
    </insert>
    
    <!-- 删除点赞记录 -->
    <delete id="deletePostLike">
        DELETE FROM post_like
        WHERE post_id = #{postId} AND user_id = #{userId}
    </delete>
    
    <!-- 根据帖子ID获取点赞列表 -->
    <select id="getPostLikesByPostId" resultMap="postLikeResultMap" parameterType="long">
        SELECT id, post_id, user_id, create_time
        FROM post_like
        WHERE post_id = #{postId}
        ORDER BY create_time DESC
    </select>
    
    <!-- 根据用户ID获取点赞列表（分页） -->
    <select id="getPostLikesByUserId" resultMap="postLikeResultMap">
        SELECT id, post_id, user_id, create_time
        FROM post_like
        WHERE user_id = #{userId}
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>
    
    <!-- 获取帖子点赞总数 -->
    <select id="getPostLikeCountByPostId" resultType="int" parameterType="long">
        SELECT COUNT(*)
        FROM post_like
        WHERE post_id = #{postId}
    </select>
    
    <!-- 获取用户点赞总数 -->
    <select id="getPostLikeCountByUserId" resultType="int" parameterType="long">
        SELECT COUNT(*)
        FROM post_like
        WHERE user_id = #{userId}
    </select>
</mapper> 
package com.forum.dao;

import com.forum.entity.Post;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Post DAO interface
 */
public interface PostDao {
    
    /**
     * Get post by ID
     * 
     * @param postId Post ID
     * @return Post object
     */
    Post getPostById(Long postId);
    
    /**
     * Insert a new post
     * 
     * @param post Post object
     * @return Number of rows affected
     */
    int insertPost(Post post);
    
    /**
     * Update post
     * 
     * @param post Post object
     * @return Number of rows affected
     */
    int updatePost(Post post);
    
    /**
     * Delete post (logical delete)
     * 
     * @param postId Post ID
     * @return Number of rows affected
     */
    int deletePost(Long postId);
    
    /**
     * Get post list with pagination
     * 
     * @param offset Offset
     * @param limit Limit
     * @return Post list
     */
    List<Post> getPostList(@Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * Get post list by category with pagination
     * 
     * @param categoryId Category ID
     * @param offset Offset
     * @param limit Limit
     * @return Post list
     */
    List<Post> getPostListByCategory(@Param("categoryId") Integer categoryId, 
                                     @Param("offset") int offset, 
                                     @Param("limit") int limit);
    
    /**
     * Get post list by user with pagination
     * 
     * @param userId User ID
     * @param offset Offset
     * @param limit Limit
     * @return Post list
     */
    List<Post> getPostListByUser(@Param("userId") Long userId, 
                                 @Param("offset") int offset, 
                                 @Param("limit") int limit);
    
    /**
     * Get total post count
     * 
     * @return Total post count
     */
    int getPostCount();
    
    /**
     * Get post count by category
     * 
     * @param categoryId Category ID
     * @return Post count
     */
    int getPostCountByCategory(Integer categoryId);
    
    /**
     * Get post count by user
     * 
     * @param userId User ID
     * @return Post count
     */
    int getPostCountByUser(Long userId);
    
    /**
     * Increment view count
     * 
     * @param postId Post ID
     * @return Number of rows affected
     */
    int incrementViewCount(Long postId);
    
    /**
     * Update like count
     * 
     * @param postId Post ID
     * @param count Like count delta
     * @return Number of rows affected
     */
    int updateLikeCount(@Param("postId") Long postId, @Param("count") int count);
    
    /**
     * Update comment count
     * 
     * @param postId Post ID
     * @param count Comment count delta
     * @return Number of rows affected
     */
    int updateCommentCount(@Param("postId") Long postId, @Param("count") int count);
    
    /**
     * Search posts by keyword
     * 
     * @param keyword Keyword
     * @param offset Offset
     * @param limit Limit
     * @return Post list
     */
    List<Post> searchPosts(@Param("keyword") String keyword, 
                          @Param("offset") int offset, 
                          @Param("limit") int limit);
    
    /**
     * Get search result count
     * 
     * @param keyword Keyword
     * @return Search result count
     */
    int getSearchCount(String keyword);
} 
package com.forum.controller;

import com.forum.entity.Post;
import com.forum.service.PostService;
import com.forum.util.PageResult;
import com.forum.util.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 帖子控制器
 */
@RestController
@RequestMapping("/api/post")
public class PostController {
    
    @Autowired
    private PostService postService;
    
    /**
     * 发布帖子
     */
    @PostMapping
    public Result<Post> publishPost(@RequestBody Post post, @RequestAttribute("userId") Long userId) {
        post.setUserId(userId); // 设置当前用户为作者
        Post publishedPost = postService.publishPost(post);
        return Result.success("发布成功", publishedPost);
    }
    
    /**
     * 更新帖子
     */
    @PutMapping("/{postId}")
    public Result<Post> updatePost(@PathVariable Long postId, 
                                  @RequestBody Post post,
                                  @RequestAttribute("userId") Long userId) {
        post.setPostId(postId);
        post.setUserId(userId); // 设置当前用户，用于权限检查
        Post updatedPost = postService.updatePost(post);
        return Result.success("更新成功", updatedPost);
    }
    
    /**
     * 删除帖子
     */
    @DeleteMapping("/{postId}")
    public Result<Boolean> deletePost(@PathVariable Long postId, @RequestAttribute("userId") Long userId) {
        boolean result = postService.deletePost(postId, userId);
        return Result.success("删除成功", result);
    }
    
    /**
     * 获取帖子详情
     */
    @GetMapping("/{postId}")
    public Result<Post> getPostById(@PathVariable Long postId) {
        // 增加浏览量
        postService.incrementViewCount(postId);
        
        Post post = postService.getPostById(postId);
        return Result.success(post);
    }
    
    /**
     * 获取帖子列表（分页）
     */
    @GetMapping("/list")
    public Result<PageResult<Post>> getPostList(@RequestParam(defaultValue = "1") int pageNum,
                                              @RequestParam(defaultValue = "10") int pageSize) {
        PageResult<Post> pageResult = postService.getPostList(pageNum, pageSize);
        return Result.success(pageResult);
    }
    
    /**
     * 根据分类获取帖子列表（分页）
     */
    @GetMapping("/category/{categoryId}")
    public Result<PageResult<Post>> getPostListByCategory(@PathVariable Integer categoryId,
                                                        @RequestParam(defaultValue = "1") int pageNum,
                                                        @RequestParam(defaultValue = "10") int pageSize) {
        PageResult<Post> pageResult = postService.getPostListByCategory(categoryId, pageNum, pageSize);
        return Result.success(pageResult);
    }
    
    /**
     * 根据用户获取帖子列表（分页）
     */
    @GetMapping("/user/{userId}")
    public Result<PageResult<Post>> getPostListByUser(@PathVariable Long userId,
                                                   @RequestParam(defaultValue = "1") int pageNum,
                                                   @RequestParam(defaultValue = "10") int pageSize) {
        PageResult<Post> pageResult = postService.getPostListByUser(userId, pageNum, pageSize);
        return Result.success(pageResult);
    }
    
    /**
     * 搜索帖子（分页）
     */
    @GetMapping("/search")
    public Result<PageResult<Post>> searchPosts(@RequestParam String keyword,
                                             @RequestParam(defaultValue = "1") int pageNum,
                                             @RequestParam(defaultValue = "10") int pageSize) {
        PageResult<Post> pageResult = postService.searchPosts(keyword, pageNum, pageSize);
        return Result.success(pageResult);
    }
    
    /**
     * 点赞帖子
     */
    @PostMapping("/{postId}/like")
    public Result<Boolean> likePost(@PathVariable Long postId, @RequestAttribute("userId") Long userId) {
        boolean result = postService.likePost(postId, userId);
        return Result.success("点赞成功", result);
    }
    
    /**
     * 取消点赞
     */
    @DeleteMapping("/{postId}/like")
    public Result<Boolean> unlikePost(@PathVariable Long postId, @RequestAttribute("userId") Long userId) {
        boolean result = postService.unlikePost(postId, userId);
        return Result.success("取消点赞成功", result);
    }
    
    /**
     * 收藏帖子
     */
    @PostMapping("/{postId}/favorite")
    public Result<Boolean> favoritePost(@PathVariable Long postId, @RequestAttribute("userId") Long userId) {
        boolean result = postService.favoritePost(postId, userId);
        return Result.success("收藏成功", result);
    }
    
    /**
     * 取消收藏
     */
    @DeleteMapping("/{postId}/favorite")
    public Result<Boolean> unfavoritePost(@PathVariable Long postId, @RequestAttribute("userId") Long userId) {
        boolean result = postService.unfavoritePost(postId, userId);
        return Result.success("取消收藏成功", result);
    }
    
    /**
     * 检查用户是否已点赞帖子
     */
    @GetMapping("/{postId}/is-liked")
    public Result<Boolean> isPostLiked(@PathVariable Long postId, @RequestAttribute("userId") Long userId) {
        boolean liked = postService.isPostLiked(postId, userId);
        return Result.success(liked);
    }
    
    /**
     * 检查用户是否已收藏帖子
     */
    @GetMapping("/{postId}/is-favorited")
    public Result<Boolean> isPostFavorited(@PathVariable Long postId, @RequestAttribute("userId") Long userId) {
        boolean favorited = postService.isPostFavorited(postId, userId);
        return Result.success(favorited);
    }
} 
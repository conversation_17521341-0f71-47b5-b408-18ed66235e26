package com.forum.dao;

import com.forum.entity.Category;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Category DAO interface
 */
public interface CategoryDao {
    
    /**
     * Get category by ID
     * 
     * @param categoryId Category ID
     * @return Category object
     */
    Category getCategoryById(Integer categoryId);
    
    /**
     * Get category by name
     * 
     * @param name Category name
     * @return Category object
     */
    Category getCategoryByName(String name);
    
    /**
     * Insert a new category
     * 
     * @param category Category object
     * @return Number of rows affected
     */
    int insertCategory(Category category);
    
    /**
     * Update category
     * 
     * @param category Category object
     * @return Number of rows affected
     */
    int updateCategory(Category category);
    
    /**
     * Delete category
     * 
     * @param categoryId Category ID
     * @return Number of rows affected
     */
    int deleteCategory(Integer categoryId);
    
    /**
     * Get all categories
     * 
     * @return Category list
     */
    List<Category> getAllCategories();
    
    /**
     * Get enabled categories
     * 
     * @return Category list
     */
    List<Category> getEnabledCategories();
    
    /**
     * Get category list with pagination
     * 
     * @param offset Offset
     * @param limit Limit
     * @return Category list
     */
    List<Category> getCategoryList(@Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * Get total category count
     * 
     * @return Total category count
     */
    int getCategoryCount();
}
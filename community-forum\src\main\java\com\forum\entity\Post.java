package com.forum.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Post entity
 */
@Data
public class Post implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * Post ID
     */
    private Long postId;

    /**
     * Post title
     */
    private String title;

    /**
     * Post content
     */
    private String content;

    /**
     * Author user ID
     */
    private Long userId;

    /**
     * Category ID
     */
    private Integer categoryId;

    /**
     * View count
     */
    private Integer viewCount;

    /**
     * Like count
     */
    private Integer likeCount;

    /**
     * Comment count
     */
    private Integer commentCount;

    /**
     * Status: 0-deleted, 1-normal, 2-pinned
     */
    private Integer status;

    /**
     * Create time
     */
    private Date createTime;

    /**
     * Update time
     */
    private Date updateTime;

    /**
     * Author information (not stored in database)
     */
    private User author;

    /**
     * Category information (not stored in database)
     */
    private Category category;

    /**
     * Whether current user has liked this post (not stored in database)
     */
    private Boolean liked;

    /**
     * Whether current user has favorited this post (not stored in database)
     */
    private Boolean favorited;
} 
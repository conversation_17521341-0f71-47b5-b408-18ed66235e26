package com.forum.service.impl;

import com.forum.dao.PostDao;
import com.forum.dao.PostFavoriteDao;
import com.forum.dao.PostLikeDao;
import com.forum.entity.Post;
import com.forum.entity.PostFavorite;
import com.forum.entity.PostLike;
import com.forum.exception.BusinessException;
import com.forum.exception.ForbiddenException;
import com.forum.exception.NotFoundException;
import com.forum.service.NotificationService;
import com.forum.service.PostService;
import com.forum.util.Constant;
import com.forum.util.PageResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 帖子服务实现类
 */
@Service
public class PostServiceImpl implements PostService {
    
    private static final Logger logger = LoggerFactory.getLogger(PostServiceImpl.class);
    
    @Autowired
    private PostDao postDao;
    
    @Autowired
    private PostLikeDao postLikeDao;
    
    @Autowired
    private PostFavoriteDao postFavoriteDao;
    
    @Autowired
    private NotificationService notificationService;
    
    @Override
    @Transactional
    public Post publishPost(Post post) {
        // 设置默认值
        Date now = new Date();
        post.setViewCount(0);
        post.setLikeCount(0);
        post.setCommentCount(0);
        post.setStatus(Constant.Post.STATUS_NORMAL);
        post.setCreateTime(now);
        post.setUpdateTime(now);
        
        // 插入数据库
        postDao.insertPost(post);
        
        logger.info("帖子发布成功: {}", post.getPostId());
        return post;
    }
    
    @Override
    @Transactional
    public Post updatePost(Post post) {
        // 检查帖子是否存在
        Post existingPost = postDao.getPostById(post.getPostId());
        if (existingPost == null) {
            throw new NotFoundException("帖子不存在");
        }
        
        // 检查是否有权限修改
        if (!existingPost.getUserId().equals(post.getUserId())) {
            throw new ForbiddenException("无权修改此帖子");
        }
        
        // 设置更新时间
        post.setUpdateTime(new Date());
        
        // 更新数据库
        postDao.updatePost(post);
        
        // 重新获取帖子信息
        Post updatedPost = postDao.getPostById(post.getPostId());
        
        logger.info("帖子更新成功: {}", post.getPostId());
        return updatedPost;
    }
    
    @Override
    @Transactional
    public boolean deletePost(Long postId, Long userId) {
        // 检查帖子是否存在
        Post post = postDao.getPostById(postId);
        if (post == null) {
            throw new NotFoundException("帖子不存在");
        }
        
        // 检查是否有权限删除
        if (!post.getUserId().equals(userId)) {
            throw new ForbiddenException("无权删除此帖子");
        }
        
        // 删除帖子（逻辑删除）
        postDao.deletePost(postId);
        
        logger.info("帖子删除成功: {}", postId);
        return true;
    }
    
    @Override
    public Post getPostById(Long postId) {
        Post post = postDao.getPostById(postId);
        if (post == null) {
            throw new NotFoundException("帖子不存在");
        }
        return post;
    }
    
    @Override
    public PageResult<Post> getPostList(int pageNum, int pageSize) {
        // 计算偏移量
        int offset = (pageNum - 1) * pageSize;
        
        // 查询帖子列表
        List<Post> postList = postDao.getPostList(offset, pageSize);
        
        // 查询总数
        int total = postDao.getPostCount();
        
        return new PageResult<>(pageNum, pageSize, total, postList);
    }
    
    @Override
    public PageResult<Post> getPostListByCategory(Integer categoryId, int pageNum, int pageSize) {
        // 计算偏移量
        int offset = (pageNum - 1) * pageSize;
        
        // 查询帖子列表
        List<Post> postList = postDao.getPostListByCategory(categoryId, offset, pageSize);
        
        // 查询总数
        int total = postDao.getPostCountByCategory(categoryId);
        
        return new PageResult<>(pageNum, pageSize, total, postList);
    }
    
    @Override
    public PageResult<Post> getPostListByUser(Long userId, int pageNum, int pageSize) {
        // 计算偏移量
        int offset = (pageNum - 1) * pageSize;
        
        // 查询帖子列表
        List<Post> postList = postDao.getPostListByUser(userId, offset, pageSize);
        
        // 查询总数
        int total = postDao.getPostCountByUser(userId);
        
        return new PageResult<>(pageNum, pageSize, total, postList);
    }
    
    @Override
    public PageResult<Post> searchPosts(String keyword, int pageNum, int pageSize) {
        // 计算偏移量
        int offset = (pageNum - 1) * pageSize;
        
        // 搜索帖子
        List<Post> postList = postDao.searchPosts(keyword, offset, pageSize);
        
        // 查询总数
        int total = postDao.getSearchCount(keyword);
        
        return new PageResult<>(pageNum, pageSize, total, postList);
    }
    
    @Override
    public void incrementViewCount(Long postId) {
        postDao.incrementViewCount(postId);
    }
    
    @Override
    @Transactional
    public boolean likePost(Long postId, Long userId) {
        // 检查帖子是否存在
        Post post = postDao.getPostById(postId);
        if (post == null) {
            throw new NotFoundException("帖子不存在");
        }
        
        // 检查是否已点赞
        if (isPostLiked(postId, userId)) {
            throw new BusinessException("已经点赞过此帖子");
        }
        
        // 创建点赞记录
        PostLike postLike = new PostLike();
        postLike.setPostId(postId);
        postLike.setUserId(userId);
        postLike.setCreateTime(new Date());
        
        // 插入数据库
        postLikeDao.insertPostLike(postLike);
        
        // 更新帖子点赞数
        postDao.updateLikeCount(postId, 1);
        
        // 发送通知（如果点赞者不是作者自己）
        if (!userId.equals(post.getUserId())) {
            notificationService.sendLikeNotification(userId, post.getUserId(), postId);
        }
        
        logger.info("帖子点赞成功: postId={}, userId={}", postId, userId);
        return true;
    }
    
    @Override
    @Transactional
    public boolean unlikePost(Long postId, Long userId) {
        // 检查帖子是否存在
        Post post = postDao.getPostById(postId);
        if (post == null) {
            throw new NotFoundException("帖子不存在");
        }
        
        // 检查是否已点赞
        if (!isPostLiked(postId, userId)) {
            throw new BusinessException("尚未点赞此帖子");
        }
        
        // 删除点赞记录
        postLikeDao.deletePostLike(postId, userId);
        
        // 更新帖子点赞数
        postDao.updateLikeCount(postId, -1);
        
        logger.info("取消帖子点赞成功: postId={}, userId={}", postId, userId);
        return true;
    }
    
    @Override
    @Transactional
    public boolean favoritePost(Long postId, Long userId) {
        // 检查帖子是否存在
        Post post = postDao.getPostById(postId);
        if (post == null) {
            throw new NotFoundException("帖子不存在");
        }
        
        // 检查是否已收藏
        if (isPostFavorited(postId, userId)) {
            throw new BusinessException("已经收藏过此帖子");
        }
        
        // 创建收藏记录
        PostFavorite postFavorite = new PostFavorite();
        postFavorite.setPostId(postId);
        postFavorite.setUserId(userId);
        postFavorite.setCreateTime(new Date());
        
        // 插入数据库
        postFavoriteDao.insertPostFavorite(postFavorite);
        
        // 发送通知（如果收藏者不是作者自己）
        if (!userId.equals(post.getUserId())) {
            notificationService.sendFavoriteNotification(userId, post.getUserId(), postId);
        }
        
        logger.info("帖子收藏成功: postId={}, userId={}", postId, userId);
        return true;
    }
    
    @Override
    @Transactional
    public boolean unfavoritePost(Long postId, Long userId) {
        // 检查帖子是否存在
        Post post = postDao.getPostById(postId);
        if (post == null) {
            throw new NotFoundException("帖子不存在");
        }
        
        // 检查是否已收藏
        if (!isPostFavorited(postId, userId)) {
            throw new BusinessException("尚未收藏此帖子");
        }
        
        // 删除收藏记录
        postFavoriteDao.deletePostFavorite(postId, userId);
        
        logger.info("取消帖子收藏成功: postId={}, userId={}", postId, userId);
        return true;
    }
    
    @Override
    public boolean isPostLiked(Long postId, Long userId) {
        return postLikeDao.getPostLike(postId, userId) != null;
    }
    
    @Override
    public boolean isPostFavorited(Long postId, Long userId) {
        return postFavoriteDao.getPostFavorite(postId, userId) != null;
    }
} 
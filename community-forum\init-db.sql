-- 初始化社区论坛数据库
CREATE DATABASE IF NOT EXISTS community_forum DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

USE community_forum;

-- 用户表
CREATE TABLE IF NOT EXISTS `user` (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'User ID',
  `username` varchar(50) NOT NULL COMMENT 'Username',
  `password` varchar(100) NOT NULL COMMENT 'Password (encrypted)',
  `email` varchar(100) NOT NULL COMMENT 'Email address',
  `avatar` varchar(255) DEFAULT NULL COMMENT 'Avatar URL',
  `nickname` varchar(50) DEFAULT NULL COMMENT 'Nickname',
  `bio` varchar(255) DEFAULT NULL COMMENT 'User bio',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT 'User status: 0-disabled, 1-enabled',
  `role` varchar(20) NOT NULL DEFAULT 'USER' COMMENT 'User role: USER, ADMIN',
  `create_time` datetime NOT NULL COMMENT 'Create time',
  `update_time` datetime NOT NULL COMMENT 'Update time',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `idx_username` (`username`),
  UNIQUE KEY `idx_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='User table';

-- 分类表
CREATE TABLE IF NOT EXISTS `category` (
  `category_id` int NOT NULL AUTO_INCREMENT COMMENT 'Category ID',
  `name` varchar(50) NOT NULL COMMENT 'Category name',
  `description` varchar(255) DEFAULT NULL COMMENT 'Category description',
  `icon` varchar(255) DEFAULT NULL COMMENT 'Category icon',
  `sort_order` int DEFAULT '0' COMMENT 'Sort order',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT 'Status: 0-disabled, 1-enabled',
  `create_time` datetime NOT NULL COMMENT 'Create time',
  `update_time` datetime NOT NULL COMMENT 'Update time',
  PRIMARY KEY (`category_id`),
  UNIQUE KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Category table';

-- 帖子表
CREATE TABLE IF NOT EXISTS `post` (
  `post_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'Post ID',
  `title` varchar(100) NOT NULL COMMENT 'Post title',
  `content` text NOT NULL COMMENT 'Post content',
  `user_id` bigint NOT NULL COMMENT 'Author user ID',
  `category_id` int NOT NULL COMMENT 'Category ID',
  `view_count` int DEFAULT '0' COMMENT 'View count',
  `like_count` int DEFAULT '0' COMMENT 'Like count',
  `comment_count` int DEFAULT '0' COMMENT 'Comment count',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT 'Status: 0-deleted, 1-normal, 2-pinned',
  `create_time` datetime NOT NULL COMMENT 'Create time',
  `update_time` datetime NOT NULL COMMENT 'Update time',
  PRIMARY KEY (`post_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Post table';

-- 评论表
CREATE TABLE IF NOT EXISTS `comment` (
  `comment_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'Comment ID',
  `content` varchar(1000) NOT NULL COMMENT 'Comment content',
  `post_id` bigint NOT NULL COMMENT 'Post ID',
  `user_id` bigint NOT NULL COMMENT 'User ID',
  `parent_id` bigint DEFAULT NULL COMMENT 'Parent comment ID, NULL if top-level comment',
  `to_user_id` bigint DEFAULT NULL COMMENT 'Reply to user ID, NULL if not a reply',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT 'Status: 0-deleted, 1-normal',
  `create_time` datetime NOT NULL COMMENT 'Create time',
  `update_time` datetime NOT NULL COMMENT 'Update time',
  PRIMARY KEY (`comment_id`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Comment table';

-- 点赞表
CREATE TABLE IF NOT EXISTS `post_like` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `post_id` bigint NOT NULL COMMENT 'Post ID',
  `user_id` bigint NOT NULL COMMENT 'User ID',
  `create_time` datetime NOT NULL COMMENT 'Create time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_post_user` (`post_id`,`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Post like table';

-- 收藏表
CREATE TABLE IF NOT EXISTS `post_favorite` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `post_id` bigint NOT NULL COMMENT 'Post ID',
  `user_id` bigint NOT NULL COMMENT 'User ID',
  `create_time` datetime NOT NULL COMMENT 'Create time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_post_user` (`post_id`,`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Post favorite table';

-- 通知表
CREATE TABLE IF NOT EXISTS `notification` (
  `notification_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'Notification ID',
  `type` varchar(20) NOT NULL COMMENT 'Notification type: COMMENT, LIKE, FAVORITE, SYSTEM',
  `sender_id` bigint DEFAULT NULL COMMENT 'Sender user ID, NULL if system notification',
  `receiver_id` bigint NOT NULL COMMENT 'Receiver user ID',
  `post_id` bigint DEFAULT NULL COMMENT 'Related post ID',
  `comment_id` bigint DEFAULT NULL COMMENT 'Related comment ID',
  `content` varchar(255) DEFAULT NULL COMMENT 'Notification content',
  `is_read` tinyint NOT NULL DEFAULT '0' COMMENT 'Read status: 0-unread, 1-read',
  `create_time` datetime NOT NULL COMMENT 'Create time',
  PRIMARY KEY (`notification_id`),
  KEY `idx_receiver_id` (`receiver_id`),
  KEY `idx_is_read` (`is_read`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Notification table';

-- 插入一些初始数据
INSERT INTO `category` (`name`, `description`, `icon`, `sort_order`, `create_time`, `update_time`) VALUES
('技术讨论', '技术相关的讨论和分享', 'fas fa-code', 1, NOW(), NOW()),
('生活分享', '日常生活的分享和交流', 'fas fa-heart', 2, NOW(), NOW()),
('问答求助', '问题求助和解答', 'fas fa-question-circle', 3, NOW(), NOW()),
('公告通知', '系统公告和重要通知', 'fas fa-bullhorn', 4, NOW(), NOW());

-- 创建管理员用户 (密码: admin123)
INSERT INTO `user` (`username`, `password`, `email`, `nickname`, `role`, `status`, `create_time`, `update_time`) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaYMountjdMYO', '<EMAIL>', '管理员', 'ADMIN', 1, NOW(), NOW());

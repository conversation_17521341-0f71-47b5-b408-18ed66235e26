package com.forum.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Comment entity
 */
@Data
public class Comment implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * Comment ID
     */
    private Long commentId;

    /**
     * Comment content
     */
    private String content;

    /**
     * Post ID
     */
    private Long postId;

    /**
     * User ID
     */
    private Long userId;

    /**
     * Parent comment ID, NULL if top-level comment
     */
    private Long parentId;

    /**
     * Reply to user ID, NULL if not a reply
     */
    private Long toUserId;

    /**
     * Status: 0-deleted, 1-normal
     */
    private Integer status;

    /**
     * Create time
     */
    private Date createTime;

    /**
     * Update time
     */
    private Date updateTime;

    /**
     * User information (not stored in database)
     */
    private User user;

    /**
     * Reply to user information (not stored in database)
     */
    private User toUser;

    /**
     * Child comments (not stored in database)
     */
    private List<Comment> children;
}
package com.forum.config;

import com.forum.util.JwtUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import org.springframework.web.socket.server.HandshakeInterceptor;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * WebSocket配置类
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {
    
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(webSocketNotificationHandler(), "/ws/notification")
                .addInterceptors(authHandshakeInterceptor())
                .setAllowedOrigins("*"); // 允许所有来源
    }
    
    @Bean
    public WebSocketNotificationHandler webSocketNotificationHandler() {
        return new WebSocketNotificationHandler();
    }
    
    @Bean
    public HandshakeInterceptor authHandshakeInterceptor() {
        return new HandshakeInterceptor() {
            @Override
            public boolean beforeHandshake(org.springframework.http.server.ServerHttpRequest request,
                                          org.springframework.http.server.ServerHttpResponse response,
                                          org.springframework.web.socket.WebSocketHandler wsHandler,
                                          Map<String, Object> attributes) throws Exception {
                // 获取HTTP请求
                if (request instanceof org.springframework.http.server.ServletServerHttpRequest) {
                    HttpServletRequest servletRequest = ((org.springframework.http.server.ServletServerHttpRequest) request).getServletRequest();
                    
                    // 从请求参数中获取token
                    String token = servletRequest.getParameter("token");
                    
                    // 验证token
                    if (token != null && JwtUtil.validateToken(token)) {
                        // 从token中获取用户ID
                        Long userId = JwtUtil.getUserIdFromToken(token);
                        
                        // 将用户ID设置到WebSocket会话属性中
                        attributes.put("userId", userId);
                        
                        return true;
                    }
                }
                
                return false; // 验证失败，拒绝连接
            }
            
            @Override
            public void afterHandshake(org.springframework.http.server.ServerHttpRequest request,
                                      org.springframework.http.server.ServerHttpResponse response,
                                      org.springframework.web.socket.WebSocketHandler wsHandler,
                                      Exception ex) {
                // 握手后的处理，不需要额外操作
            }
        };
    }
} 
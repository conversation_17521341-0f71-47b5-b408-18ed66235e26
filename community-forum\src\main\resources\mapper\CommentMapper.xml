<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.forum.dao.CommentDao">
    <!-- 结果映射 -->
    <resultMap id="commentResultMap" type="Comment">
        <id property="commentId" column="comment_id"/>
        <result property="content" column="content"/>
        <result property="postId" column="post_id"/>
        <result property="userId" column="user_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="toUserId" column="to_user_id"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <!-- 关联用户信息 -->
        <association property="user" javaType="User">
            <id property="userId" column="author_id"/>
            <result property="username" column="author_username"/>
            <result property="nickname" column="author_nickname"/>
            <result property="avatar" column="author_avatar"/>
        </association>
        <!-- 关联回复目标用户信息 -->
        <association property="toUser" javaType="User">
            <id property="userId" column="to_author_id"/>
            <result property="username" column="to_author_username"/>
            <result property="nickname" column="to_author_nickname"/>
            <result property="avatar" column="to_author_avatar"/>
        </association>
    </resultMap>
    
    <!-- 基础列 -->
    <sql id="baseColumns">
        c.comment_id, c.content, c.post_id, c.user_id, c.parent_id, c.to_user_id, c.status, c.create_time, c.update_time
    </sql>
    
    <!-- 关联查询列 -->
    <sql id="joinColumns">
        <include refid="baseColumns"/>,
        u1.user_id as author_id, u1.username as author_username, u1.nickname as author_nickname, u1.avatar as author_avatar,
        u2.user_id as to_author_id, u2.username as to_author_username, u2.nickname as to_author_nickname, u2.avatar as to_author_avatar
    </sql>
    
    <!-- 关联查询 -->
    <sql id="joins">
        LEFT JOIN user u1 ON c.user_id = u1.user_id
        LEFT JOIN user u2 ON c.to_user_id = u2.user_id
    </sql>
    
    <!-- 根据ID获取评论 -->
    <select id="getCommentById" resultMap="commentResultMap" parameterType="long">
        SELECT <include refid="joinColumns"/>
        FROM comment c
        <include refid="joins"/>
        WHERE c.comment_id = #{commentId}
    </select>
    
    <!-- 插入新评论 -->
    <insert id="insertComment" parameterType="Comment" useGeneratedKeys="true" keyProperty="commentId">
        INSERT INTO comment (content, post_id, user_id, parent_id, to_user_id, status, create_time, update_time)
        VALUES (#{content}, #{postId}, #{userId}, #{parentId}, #{toUserId}, #{status}, #{createTime}, #{updateTime})
    </insert>
    
    <!-- 更新评论 -->
    <update id="updateComment" parameterType="Comment">
        UPDATE comment
        <set>
            <if test="content != null">content = #{content},</if>
            <if test="status != null">status = #{status},</if>
            update_time = #{updateTime}
        </set>
        WHERE comment_id = #{commentId} AND user_id = #{userId}
    </update>
    
    <!-- 删除评论（逻辑删除） -->
    <update id="deleteComment" parameterType="long">
        UPDATE comment
        SET status = 0, update_time = NOW()
        WHERE comment_id = #{commentId}
    </update>
    
    <!-- 根据帖子ID获取顶级评论（分页） -->
    <select id="getCommentsByPostId" resultMap="commentResultMap">
        SELECT <include refid="joinColumns"/>
        FROM comment c
        <include refid="joins"/>
        WHERE c.post_id = #{postId} AND c.status = 1 AND c.parent_id IS NULL
        ORDER BY c.create_time ASC
        LIMIT #{offset}, #{limit}
    </select>
    
    <!-- 获取子评论 -->
    <select id="getChildComments" resultMap="commentResultMap" parameterType="long">
        SELECT <include refid="joinColumns"/>
        FROM comment c
        <include refid="joins"/>
        WHERE c.parent_id = #{parentId} AND c.status = 1
        ORDER BY c.create_time ASC
    </select>
    
    <!-- 根据用户ID获取评论（分页） -->
    <select id="getCommentsByUserId" resultMap="commentResultMap">
        SELECT <include refid="joinColumns"/>
        FROM comment c
        <include refid="joins"/>
        WHERE c.user_id = #{userId} AND c.status = 1
        ORDER BY c.create_time DESC
        LIMIT #{offset}, #{limit}
    </select>
    
    <!-- 获取帖子的评论总数 -->
    <select id="getCommentCountByPostId" resultType="int" parameterType="long">
        SELECT COUNT(*)
        FROM comment
        WHERE post_id = #{postId} AND status = 1
    </select>
    
    <!-- 获取用户的评论总数 -->
    <select id="getCommentCountByUserId" resultType="int" parameterType="long">
        SELECT COUNT(*)
        FROM comment
        WHERE user_id = #{userId} AND status = 1
    </select>
</mapper> 
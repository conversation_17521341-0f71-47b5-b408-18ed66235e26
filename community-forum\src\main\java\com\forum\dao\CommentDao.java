package com.forum.dao;

import com.forum.entity.Comment;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Comment DAO interface
 */
public interface CommentDao {
    
    /**
     * Get comment by ID
     * 
     * @param commentId Comment ID
     * @return Comment object
     */
    Comment getCommentById(Long commentId);
    
    /**
     * Insert a new comment
     * 
     * @param comment Comment object
     * @return Number of rows affected
     */
    int insertComment(Comment comment);
    
    /**
     * Update comment
     * 
     * @param comment Comment object
     * @return Number of rows affected
     */
    int updateComment(Comment comment);
    
    /**
     * Delete comment (logical delete)
     * 
     * @param commentId Comment ID
     * @return Number of rows affected
     */
    int deleteComment(Long commentId);
    
    /**
     * Get top-level comments by post ID with pagination
     * 
     * @param postId Post ID
     * @param offset Offset
     * @param limit Limit
     * @return Comment list
     */
    List<Comment> getCommentsByPostId(@Param("postId") Long postId, 
                                     @Param("offset") int offset, 
                                     @Param("limit") int limit);
    
    /**
     * Get child comments by parent ID
     * 
     * @param parentId Parent comment ID
     * @return Comment list
     */
    List<Comment> getChildComments(Long parentId);
    
    /**
     * Get comments by user ID with pagination
     * 
     * @param userId User ID
     * @param offset Offset
     * @param limit Limit
     * @return Comment list
     */
    List<Comment> getCommentsByUserId(@Param("userId") Long userId, 
                                     @Param("offset") int offset, 
                                     @Param("limit") int limit);
    
    /**
     * Get comment count by post ID
     * 
     * @param postId Post ID
     * @return Comment count
     */
    int getCommentCountByPostId(Long postId);
    
    /**
     * Get comment count by user ID
     * 
     * @param userId User ID
     * @return Comment count
     */
    int getCommentCountByUserId(Long userId);
} 
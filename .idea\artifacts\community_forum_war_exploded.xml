<component name="ArtifactManager">
  <artifact type="exploded-war" name="community-forum:war exploded">
    <output-path>$PROJECT_DIR$/community-forum/target/community-forum</output-path>
    <properties id="maven-jee-properties">
      <options>
        <exploded>true</exploded>
        <module>community-forum</module>
        <packaging>war</packaging>
      </options>
    </properties>
    <root id="root">
      <element id="directory" name="WEB-INF">
        <element id="directory" name="classes">
          <element id="module-output" name="community-forum" />
        </element>
        <element id="directory" name="lib">
          <element id="library" level="project" name="Maven: org.springframework:spring-webmvc:5.3.20" />
          <element id="library" level="project" name="Maven: org.springframework:spring-aop:5.3.20" />
          <element id="library" level="project" name="Maven: org.springframework:spring-beans:5.3.20" />
          <element id="library" level="project" name="Maven: org.springframework:spring-context:5.3.20" />
          <element id="library" level="project" name="Maven: org.springframework:spring-core:5.3.20" />
          <element id="library" level="project" name="Maven: org.springframework:spring-jcl:5.3.20" />
          <element id="library" level="project" name="Maven: org.springframework:spring-expression:5.3.20" />
          <element id="library" level="project" name="Maven: org.springframework:spring-web:5.3.20" />
          <element id="library" level="project" name="Maven: org.springframework:spring-jdbc:5.3.20" />
          <element id="library" level="project" name="Maven: org.springframework:spring-tx:5.3.20" />
          <element id="library" level="project" name="Maven: org.springframework:spring-aspects:5.3.20" />
          <element id="library" level="project" name="Maven: org.aspectj:aspectjweaver:1.9.7" />
          <element id="library" level="project" name="Maven: org.springframework:spring-context-support:5.3.20" />
          <element id="library" level="project" name="Maven: org.springframework.security:spring-security-web:5.6.3" />
          <element id="library" level="project" name="Maven: org.springframework.security:spring-security-core:5.6.3" />
          <element id="library" level="project" name="Maven: org.springframework.security:spring-security-config:5.6.3" />
          <element id="library" level="project" name="Maven: org.springframework.security:spring-security-crypto:5.6.3" />
          <element id="library" level="project" name="Maven: org.mybatis:mybatis:3.5.9" />
          <element id="library" level="project" name="Maven: org.mybatis:mybatis-spring:2.0.7" />
          <element id="library" level="project" name="Maven: mysql:mysql-connector-java:8.0.28" />
          <element id="library" level="project" name="Maven: com.google.protobuf:protobuf-java:3.11.4" />
          <element id="library" level="project" name="Maven: com.alibaba:druid:1.2.9" />
          <element id="library" level="project" name="Maven: com.fasterxml.jackson.core:jackson-databind:2.13.3" />
          <element id="library" level="project" name="Maven: com.fasterxml.jackson.core:jackson-annotations:2.13.3" />
          <element id="library" level="project" name="Maven: com.fasterxml.jackson.core:jackson-core:2.13.3" />
          <element id="library" level="project" name="Maven: jstl:jstl:1.2" />
          <element id="library" level="project" name="Maven: commons-fileupload:commons-fileupload:1.4" />
          <element id="library" level="project" name="Maven: commons-io:commons-io:2.2" />
          <element id="library" level="project" name="Maven: org.springframework.data:spring-data-redis:2.6.4" />
          <element id="library" level="project" name="Maven: org.springframework.data:spring-data-keyvalue:2.6.4" />
          <element id="library" level="project" name="Maven: org.springframework.data:spring-data-commons:2.6.4" />
          <element id="library" level="project" name="Maven: org.springframework:spring-oxm:5.3.19" />
          <element id="library" level="project" name="Maven: redis.clients:jedis:3.8.0" />
          <element id="library" level="project" name="Maven: org.apache.commons:commons-pool2:2.11.1" />
          <element id="library" level="project" name="Maven: org.apache.logging.log4j:log4j-core:2.17.2" />
          <element id="library" level="project" name="Maven: org.apache.logging.log4j:log4j-api:2.17.2" />
          <element id="library" level="project" name="Maven: org.slf4j:slf4j-api:1.7.36" />
          <element id="library" level="project" name="Maven: org.apache.logging.log4j:log4j-slf4j-impl:2.17.2" />
          <element id="library" level="project" name="Maven: org.springframework:spring-websocket:5.3.20" />
          <element id="library" level="project" name="Maven: org.springframework:spring-messaging:5.3.20" />
          <element id="library" level="project" name="Maven: io.jsonwebtoken:jjwt:0.9.1" />
          <element id="library" level="project" name="Maven: com.google.code.gson:gson:2.9.0" />
        </element>
      </element>
      <element id="directory" name="META-INF">
        <element id="file-copy" path="$PROJECT_DIR$/community-forum/target/community-forum/META-INF/MANIFEST.MF" />
      </element>
      <element id="javaee-facet-resources" facet="community-forum/web/Web" />
    </root>
  </artifact>
</component>
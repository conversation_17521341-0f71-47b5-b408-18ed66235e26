/**
 * 社区论坛主要JavaScript文件
 */

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
    // 初始化功能
    initApp();
});

/**
 * 初始化应用
 */
function initApp() {
    // 初始化通知WebSocket连接
    initNotificationWebSocket();
    
    // 初始化表单验证
    initFormValidation();
    
    // 初始化帖子交互功能
    initPostInteractions();
    
    // 初始化评论功能
    initCommentFunctions();
}

/**
 * 初始化通知WebSocket连接
 */
function initNotificationWebSocket() {
    // 检查用户是否已登录
    const token = localStorage.getItem('token');
    if (!token) return;
    
    // 创建WebSocket连接
    const ws = new WebSocket(`ws://${window.location.host}/community-forum/notifications?token=${token}`);
    
    // 连接打开时的处理
    ws.onopen = function() {
        console.log('WebSocket连接已建立');
    };
    
    // 接收到消息时的处理
    ws.onmessage = function(event) {
        const data = JSON.parse(event.data);
        
        // 更新通知计数
        updateNotificationCount();
        
        // 显示通知提示
        showNotificationToast(data);
    };
    
    // 连接关闭时的处理
    ws.onclose = function() {
        console.log('WebSocket连接已关闭');
        
        // 5秒后尝试重新连接
        setTimeout(initNotificationWebSocket, 5000);
    };
    
    // 连接错误时的处理
    ws.onerror = function(error) {
        console.error('WebSocket错误:', error);
    };
}

/**
 * 更新通知计数
 */
function updateNotificationCount() {
    // 检查用户是否已登录
    const token = localStorage.getItem('token');
    if (!token) return;
    
    // 发送请求获取未读通知数
    fetch('/community-forum/api/notification/unread/count', {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            const count = data.data;
            const badge = document.querySelector('.notification-badge .badge');
            
            if (badge) {
                if (count > 0) {
                    badge.textContent = count > 99 ? '99+' : count;
                    badge.style.display = 'flex';
                } else {
                    badge.style.display = 'none';
                }
            }
        }
    })
    .catch(error => console.error('获取通知数失败:', error));
}

/**
 * 显示通知提示
 */
function showNotificationToast(data) {
    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = 'notification-toast';
    toast.innerHTML = `
        <div class="notification-toast-content">
            <div class="notification-toast-title">新通知</div>
            <div class="notification-toast-message">${data.notification.content}</div>
        </div>
        <button class="notification-toast-close">&times;</button>
    `;
    
    // 添加到页面
    document.body.appendChild(toast);
    
    // 添加关闭按钮事件
    toast.querySelector('.notification-toast-close').addEventListener('click', function() {
        document.body.removeChild(toast);
    });
    
    // 5秒后自动关闭
    setTimeout(function() {
        if (document.body.contains(toast)) {
            document.body.removeChild(toast);
        }
    }, 5000);
}

/**
 * 初始化表单验证
 */
function initFormValidation() {
    // 获取所有需要验证的表单
    const forms = document.querySelectorAll('.needs-validation');
    
    // 遍历表单添加验证
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        }, false);
    });
}

/**
 * 初始化帖子交互功能
 */
function initPostInteractions() {
    // 点赞功能
    initLikeButtons();
    
    // 收藏功能
    initFavoriteButtons();
}

/**
 * 初始化点赞按钮
 */
function initLikeButtons() {
    // 获取所有点赞按钮
    const likeButtons = document.querySelectorAll('.like-button');
    
    // 遍历按钮添加事件
    likeButtons.forEach(button => {
        button.addEventListener('click', function() {
            // 检查用户是否已登录
            const token = localStorage.getItem('token');
            if (!token) {
                window.location.href = '/community-forum/login';
                return;
            }
            
            const postId = this.dataset.postId;
            const isLiked = this.classList.contains('liked');
            
            // 发送请求
            fetch(`/community-forum/api/post/${postId}/like`, {
                method: isLiked ? 'DELETE' : 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    // 更新按钮状态
                    this.classList.toggle('liked');
                    
                    // 更新点赞数
                    const countElement = this.querySelector('.like-count');
                    if (countElement) {
                        let count = parseInt(countElement.textContent);
                        count = isLiked ? count - 1 : count + 1;
                        countElement.textContent = count;
                    }
                }
            })
            .catch(error => console.error('点赞操作失败:', error));
        });
    });
}

/**
 * 初始化收藏按钮
 */
function initFavoriteButtons() {
    // 获取所有收藏按钮
    const favoriteButtons = document.querySelectorAll('.favorite-button');
    
    // 遍历按钮添加事件
    favoriteButtons.forEach(button => {
        button.addEventListener('click', function() {
            // 检查用户是否已登录
            const token = localStorage.getItem('token');
            if (!token) {
                window.location.href = '/community-forum/login';
                return;
            }
            
            const postId = this.dataset.postId;
            const isFavorited = this.classList.contains('favorited');
            
            // 发送请求
            fetch(`/community-forum/api/post/${postId}/favorite`, {
                method: isFavorited ? 'DELETE' : 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    // 更新按钮状态
                    this.classList.toggle('favorited');
                    
                    // 更新收藏数
                    const countElement = this.querySelector('.favorite-count');
                    if (countElement) {
                        let count = parseInt(countElement.textContent);
                        count = isFavorited ? count - 1 : count + 1;
                        countElement.textContent = count;
                    }
                }
            })
            .catch(error => console.error('收藏操作失败:', error));
        });
    });
}

/**
 * 初始化评论功能
 */
function initCommentFunctions() {
    // 评论提交
    initCommentForm();
    
    // 回复功能
    initReplyButtons();
}

/**
 * 初始化评论表单
 */
function initCommentForm() {
    // 获取评论表单
    const commentForm = document.querySelector('#comment-form');
    if (!commentForm) return;
    
    // 添加提交事件
    commentForm.addEventListener('submit', function(event) {
        event.preventDefault();
        
        // 检查用户是否已登录
        const token = localStorage.getItem('token');
        if (!token) {
            window.location.href = '/community-forum/login';
            return;
        }
        
        // 获取表单数据
        const postId = this.dataset.postId;
        const content = this.querySelector('textarea[name="content"]').value;
        const parentId = this.querySelector('input[name="parentId"]').value;
        const toUserId = this.querySelector('input[name="toUserId"]').value;
        
        // 发送请求
        fetch('/community-forum/api/comment', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                postId: postId,
                content: content,
                parentId: parentId || null,
                toUserId: toUserId || null
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                // 刷新页面显示新评论
                window.location.reload();
            }
        })
        .catch(error => console.error('评论提交失败:', error));
    });
}

/**
 * 初始化回复按钮
 */
function initReplyButtons() {
    // 获取所有回复按钮
    const replyButtons = document.querySelectorAll('.reply-button');
    
    // 遍历按钮添加事件
    replyButtons.forEach(button => {
        button.addEventListener('click', function() {
            // 检查用户是否已登录
            const token = localStorage.getItem('token');
            if (!token) {
                window.location.href = '/community-forum/login';
                return;
            }
            
            const commentId = this.dataset.commentId;
            const userId = this.dataset.userId;
            const username = this.dataset.username;
            
            // 设置回复表单
            const commentForm = document.querySelector('#comment-form');
            if (commentForm) {
                commentForm.querySelector('input[name="parentId"]').value = commentId;
                commentForm.querySelector('input[name="toUserId"]').value = userId;
                
                const textarea = commentForm.querySelector('textarea[name="content"]');
                textarea.value = `@${username} `;
                textarea.focus();
                
                // 滚动到表单
                commentForm.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });
}

/**
 * 用户登录
 */
function login(event) {
    event.preventDefault();
    
    // 获取表单数据
    const form = document.querySelector('#login-form');
    const username = form.querySelector('input[name="username"]').value;
    const password = form.querySelector('input[name="password"]').value;
    
    // 发送请求
    fetch('/community-forum/api/user/login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            username: username,
            password: password
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            // 保存token
            localStorage.setItem('token', data.data.token);
            
            // 保存用户信息
            localStorage.setItem('user', JSON.stringify(data.data.user));
            
            // 跳转到首页
            window.location.href = '/community-forum/';
        } else {
            // 显示错误消息
            const errorElement = document.querySelector('#login-error');
            if (errorElement) {
                errorElement.textContent = data.message;
                errorElement.style.display = 'block';
            }
        }
    })
    .catch(error => console.error('登录失败:', error));
}

/**
 * 用户注册
 */
function register(event) {
    event.preventDefault();
    
    // 获取表单数据
    const form = document.querySelector('#register-form');
    const username = form.querySelector('input[name="username"]').value;
    const password = form.querySelector('input[name="password"]').value;
    const email = form.querySelector('input[name="email"]').value;
    const nickname = form.querySelector('input[name="nickname"]').value;
    
    // 发送请求
    fetch('/community-forum/api/user/register', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            username: username,
            password: password,
            email: email,
            nickname: nickname
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            // 跳转到登录页
            window.location.href = '/community-forum/login?registered=true';
        } else {
            // 显示错误消息
            const errorElement = document.querySelector('#register-error');
            if (errorElement) {
                errorElement.textContent = data.message;
                errorElement.style.display = 'block';
            }
        }
    })
    .catch(error => console.error('注册失败:', error));
}

/**
 * 用户登出
 */
function logout() {
    // 清除本地存储
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    
    // 跳转到首页
    window.location.href = '/community-forum/';
} 
package com.forum.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * User entity
 */
@Data
public class User implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * User ID
     */
    private Long userId;

    /**
     * Username
     */
    private String username;

    /**
     * Password (encrypted)
     */
    private String password;

    /**
     * Email address
     */
    private String email;

    /**
     * Avatar URL
     */
    private String avatar;

    /**
     * Nickname
     */
    private String nickname;

    /**
     * User bio
     */
    private String bio;

    /**
     * User status: 0-disabled, 1-enabled
     */
    private Integer status;

    /**
     * User role: USER, ADMIN
     */
    private String role;

    /**
     * Create time
     */
    private Date createTime;

    /**
     * Update time
     */
    private Date updateTime;
} 
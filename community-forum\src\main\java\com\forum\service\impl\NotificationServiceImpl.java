package com.forum.service.impl;

import com.forum.config.WebSocketNotificationHandler;
import com.forum.dao.NotificationDao;
import com.forum.dao.PostDao;
import com.forum.dao.UserDao;
import com.forum.entity.Notification;
import com.forum.entity.Post;
import com.forum.entity.User;
import com.forum.exception.NotFoundException;
import com.forum.service.NotificationService;
import com.forum.util.Constant;
import com.forum.util.PageResult;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通知服务实现类
 */
@Service
public class NotificationServiceImpl implements NotificationService {
    
    private static final Logger logger = LoggerFactory.getLogger(NotificationServiceImpl.class);
    
    @Autowired
    private NotificationDao notificationDao;
    
    @Autowired
    private UserDao userDao;
    
    @Autowired
    private PostDao postDao;
    
    private Gson gson = new Gson();
    
    @Override
    @Transactional
    public void sendCommentNotification(Long senderId, Long receiverId, Long postId, Long commentId) {
        // 不给自己发送通知
        if (senderId.equals(receiverId)) {
            return;
        }
        
        // 检查用户是否存在
        User sender = userDao.getUserById(senderId);
        User receiver = userDao.getUserById(receiverId);
        if (sender == null || receiver == null) {
            throw new NotFoundException("用户不存在");
        }
        
        // 检查帖子是否存在
        Post post = postDao.getPostById(postId);
        if (post == null) {
            throw new NotFoundException("帖子不存在");
        }
        
        // 创建通知
        Notification notification = new Notification();
        notification.setType(Constant.Notification.TYPE_COMMENT);
        notification.setSenderId(senderId);
        notification.setReceiverId(receiverId);
        notification.setPostId(postId);
        notification.setCommentId(commentId);
        notification.setContent(sender.getNickname() + " 评论了你的帖子 " + post.getTitle());
        notification.setIsRead(Constant.Notification.STATUS_UNREAD);
        notification.setCreateTime(new Date());
        
        // 插入数据库
        notificationDao.insertNotification(notification);
        
        // 发送WebSocket通知
        sendWebSocketNotification(notification);
        
        logger.info("发送评论通知成功: senderId={}, receiverId={}, postId={}, commentId={}", 
                   senderId, receiverId, postId, commentId);
    }
    
    @Override
    @Transactional
    public void sendLikeNotification(Long senderId, Long receiverId, Long postId) {
        // 不给自己发送通知
        if (senderId.equals(receiverId)) {
            return;
        }
        
        // 检查用户是否存在
        User sender = userDao.getUserById(senderId);
        User receiver = userDao.getUserById(receiverId);
        if (sender == null || receiver == null) {
            throw new NotFoundException("用户不存在");
        }
        
        // 检查帖子是否存在
        Post post = postDao.getPostById(postId);
        if (post == null) {
            throw new NotFoundException("帖子不存在");
        }
        
        // 创建通知
        Notification notification = new Notification();
        notification.setType(Constant.Notification.TYPE_LIKE);
        notification.setSenderId(senderId);
        notification.setReceiverId(receiverId);
        notification.setPostId(postId);
        notification.setContent(sender.getNickname() + " 点赞了你的帖子 " + post.getTitle());
        notification.setIsRead(Constant.Notification.STATUS_UNREAD);
        notification.setCreateTime(new Date());
        
        // 插入数据库
        notificationDao.insertNotification(notification);
        
        // 发送WebSocket通知
        sendWebSocketNotification(notification);
        
        logger.info("发送点赞通知成功: senderId={}, receiverId={}, postId={}", 
                   senderId, receiverId, postId);
    }
    
    @Override
    @Transactional
    public void sendFavoriteNotification(Long senderId, Long receiverId, Long postId) {
        // 不给自己发送通知
        if (senderId.equals(receiverId)) {
            return;
        }
        
        // 检查用户是否存在
        User sender = userDao.getUserById(senderId);
        User receiver = userDao.getUserById(receiverId);
        if (sender == null || receiver == null) {
            throw new NotFoundException("用户不存在");
        }
        
        // 检查帖子是否存在
        Post post = postDao.getPostById(postId);
        if (post == null) {
            throw new NotFoundException("帖子不存在");
        }
        
        // 创建通知
        Notification notification = new Notification();
        notification.setType(Constant.Notification.TYPE_FAVORITE);
        notification.setSenderId(senderId);
        notification.setReceiverId(receiverId);
        notification.setPostId(postId);
        notification.setContent(sender.getNickname() + " 收藏了你的帖子 " + post.getTitle());
        notification.setIsRead(Constant.Notification.STATUS_UNREAD);
        notification.setCreateTime(new Date());
        
        // 插入数据库
        notificationDao.insertNotification(notification);
        
        // 发送WebSocket通知
        sendWebSocketNotification(notification);
        
        logger.info("发送收藏通知成功: senderId={}, receiverId={}, postId={}", 
                   senderId, receiverId, postId);
    }
    
    @Override
    @Transactional
    public void sendSystemNotification(Long receiverId, String content) {
        // 检查用户是否存在
        User receiver = userDao.getUserById(receiverId);
        if (receiver == null) {
            throw new NotFoundException("用户不存在");
        }
        
        // 创建通知
        Notification notification = new Notification();
        notification.setType(Constant.Notification.TYPE_SYSTEM);
        notification.setReceiverId(receiverId);
        notification.setContent(content);
        notification.setIsRead(Constant.Notification.STATUS_UNREAD);
        notification.setCreateTime(new Date());
        
        // 插入数据库
        notificationDao.insertNotification(notification);
        
        // 发送WebSocket通知
        sendWebSocketNotification(notification);
        
        logger.info("发送系统通知成功: receiverId={}, content={}", receiverId, content);
    }
    
    @Override
    public PageResult<Notification> getNotifications(Long receiverId, int pageNum, int pageSize) {
        // 计算偏移量
        int offset = (pageNum - 1) * pageSize;
        
        // 查询通知列表
        List<Notification> notificationList = notificationDao.getNotificationsByReceiverId(receiverId, offset, pageSize);
        
        // 查询总数
        int total = notificationDao.getNotificationCountByReceiverId(receiverId);
        
        return new PageResult<>(pageNum, pageSize, total, notificationList);
    }
    
    @Override
    public List<Notification> getUnreadNotifications(Long receiverId) {
        return notificationDao.getUnreadNotifications(receiverId);
    }
    
    @Override
    public int getUnreadNotificationCount(Long receiverId) {
        return notificationDao.getUnreadNotificationCount(receiverId);
    }
    
    @Override
    @Transactional
    public boolean markAsRead(Long notificationId) {
        // 检查通知是否存在
        Notification notification = notificationDao.getNotificationById(notificationId);
        if (notification == null) {
            throw new NotFoundException("通知不存在");
        }
        
        // 标记为已读
        notificationDao.markAsRead(notificationId);
        
        logger.info("通知标记为已读成功: {}", notificationId);
        return true;
    }
    
    @Override
    @Transactional
    public boolean markAllAsRead(Long receiverId) {
        // 标记所有通知为已读
        notificationDao.markAllAsRead(receiverId);
        
        logger.info("所有通知标记为已读成功: receiverId={}", receiverId);
        return true;
    }
    
    @Override
    @Transactional
    public boolean deleteNotification(Long notificationId) {
        // 检查通知是否存在
        Notification notification = notificationDao.getNotificationById(notificationId);
        if (notification == null) {
            throw new NotFoundException("通知不存在");
        }
        
        // 删除通知
        notificationDao.deleteNotification(notificationId);
        
        logger.info("通知删除成功: {}", notificationId);
        return true;
    }
    
    @Override
    @Transactional
    public boolean batchDeleteNotifications(Long receiverId, List<Long> notificationIds) {
        // 批量删除通知
        notificationDao.batchDeleteNotifications(receiverId, notificationIds);
        
        logger.info("批量删除通知成功: receiverId={}, count={}", receiverId, notificationIds.size());
        return true;
    }
    
    /**
     * 发送WebSocket通知
     */
    private void sendWebSocketNotification(Notification notification) {
        try {
            // 构建通知消息
            Map<String, Object> message = new HashMap<>();
            message.put("type", notification.getType());
            message.put("notification", notification);
            
            // 发送WebSocket消息
            WebSocketNotificationHandler.sendNotification(notification.getReceiverId(), gson.toJson(message));
        } catch (Exception e) {
            logger.error("发送WebSocket通知失败", e);
        }
    }
} 
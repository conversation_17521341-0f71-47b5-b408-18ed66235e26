package com.forum.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Category entity
 */
@Data
public class Category implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * Category ID
     */
    private Integer categoryId;

    /**
     * Category name
     */
    private String name;

    /**
     * Category description
     */
    private String description;

    /**
     * Category icon
     */
    private String icon;

    /**
     * Sort order
     */
    private Integer sortOrder;

    /**
     * Status: 0-disabled, 1-enabled
     */
    private Integer status;

    /**
     * Create time
     */
    private Date createTime;

    /**
     * Update time
     */
    private Date updateTime;
} 
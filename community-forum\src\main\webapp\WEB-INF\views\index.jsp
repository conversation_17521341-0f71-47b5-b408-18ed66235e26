<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${pageTitle}</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/community-forum/static/css/style.css">
</head>
<body>
    <!-- 头部导航 -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="/community-forum/" class="logo">社区论坛</a>
                
                <div class="search-form">
                    <form action="/community-forum/search" method="get">
                        <input type="text" name="keyword" class="search-input" placeholder="搜索帖子..." required>
                        <button type="submit" class="search-btn">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>
                
                <div class="nav-links">
                    <a href="/community-forum/publish" class="btn btn-primary">发布帖子</a>
                    
                    <div id="user-area">
                        <!-- 用户登录后显示 -->
                        <div class="user-menu" style="display: none;" id="logged-in-menu">
                            <div class="notification-badge">
                                <a href="/community-forum/notifications">
                                    <i class="fas fa-bell"></i>
                                </a>
                                <span class="badge" style="display: none;">0</span>
                            </div>
                            
                            <div class="dropdown">
                                <div class="user-info">
                                    <img src="/community-forum/static/images/avatar/default.svg" alt="头像" class="avatar" id="user-avatar">
                                    <span id="user-nickname"></span>
                                </div>
                                
                                <div class="dropdown-content">
                                    <a href="/community-forum/user/profile" id="user-profile-link">
                                        <i class="fas fa-user"></i> 个人中心
                                    </a>
                                    <a href="/community-forum/user/posts" id="user-posts-link">
                                        <i class="fas fa-file-alt"></i> 我的帖子
                                    </a>
                                    <a href="/community-forum/user/favorites">
                                        <i class="fas fa-star"></i> 我的收藏
                                    </a>
                                    <a href="/community-forum/user/settings">
                                        <i class="fas fa-cog"></i> 设置
                                    </a>
                                    <a href="javascript:void(0);" onclick="logout()">
                                        <i class="fas fa-sign-out-alt"></i> 退出
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 用户未登录显示 -->
                        <div id="not-logged-in-menu">
                            <a href="/community-forum/login" class="btn btn-outline">登录</a>
                            <a href="/community-forum/register" class="btn btn-primary">注册</a>
                        </div>
                    </div>
                </div>
            </nav>
        </div>
    </header>
    
    <!-- 主要内容区 -->
    <main class="container main">
        <!-- 内容区 -->
        <div class="content">
            <!-- 帖子列表 -->
            <div class="post-list" id="post-list">
                <!-- 帖子将通过JavaScript动态加载 -->
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                </div>
            </div>
            
            <!-- 分页 -->
            <div class="pagination" id="pagination">
                <!-- 分页将通过JavaScript动态生成 -->
            </div>
        </div>
        
        <!-- 侧边栏 -->
        <div class="sidebar">
            <!-- 分类列表 -->
            <div class="sidebar-section">
                <div class="sidebar-header">
                    <i class="fas fa-list"></i> 分类
                </div>
                <div class="sidebar-content">
                    <ul class="category-list">
                        <c:forEach items="${categories}" var="category">
                            <li>
                                <a href="/community-forum/category/${category.categoryId}">
                                    <i class="fas ${category.icon} icon"></i>
                                    ${category.name}
                                    <span class="count" id="category-count-${category.categoryId}">0</span>
                                </a>
                            </li>
                        </c:forEach>
                    </ul>
                </div>
            </div>
            
            <!-- 热门帖子 -->
            <div class="sidebar-section">
                <div class="sidebar-header">
                    <i class="fas fa-fire"></i> 热门帖子
                </div>
                <div class="sidebar-content" id="hot-posts">
                    <!-- 热门帖子将通过JavaScript动态加载 -->
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i> 加载中...
                    </div>
                </div>
            </div>
            
            <!-- 社区信息 -->
            <div class="sidebar-section">
                <div class="sidebar-header">
                    <i class="fas fa-info-circle"></i> 社区信息
                </div>
                <div class="sidebar-content">
                    <p>欢迎来到我们的社区论坛！</p>
                    <p>这里是一个开放、友善的交流平台，希望大家能够在这里分享知识、交流经验。</p>
                    <p class="mt-2">
                        <i class="fas fa-users"></i> <span id="user-count">0</span> 位用户
                    </p>
                    <p>
                        <i class="fas fa-file-alt"></i> <span id="post-count">0</span> 篇帖子
                    </p>
                    <p>
                        <i class="fas fa-comment"></i> <span id="comment-count">0</span> 条评论
                    </p>
                </div>
            </div>
        </div>
    </main>
    
    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <p>&copy; 2023 社区论坛. 保留所有权利.</p>
            </div>
        </div>
    </footer>
    
    <script src="/community-forum/static/js/main.js"></script>
    <script>
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 检查用户是否已登录
            const token = localStorage.getItem('token');
            const user = JSON.parse(localStorage.getItem('user') || '{}');
            
            if (token && user) {
                // 已登录，显示用户菜单
                document.getElementById('logged-in-menu').style.display = 'flex';
                document.getElementById('not-logged-in-menu').style.display = 'none';
                
                // 设置用户信息
                document.getElementById('user-avatar').src = user.avatar || '/community-forum/static/images/avatar/default.svg';
                document.getElementById('user-nickname').textContent = user.nickname;
                document.getElementById('user-profile-link').href = '/community-forum/user/' + user.userId;
                document.getElementById('user-posts-link').href = '/community-forum/user/posts/' + user.userId;
                
                // 获取未读通知数
                updateNotificationCount();
            } else {
                // 未登录，显示登录注册按钮
                document.getElementById('logged-in-menu').style.display = 'none';
                document.getElementById('not-logged-in-menu').style.display = 'flex';
            }
            
            // 加载帖子列表
            loadPosts(1);
            
            // 加载热门帖子
            loadHotPosts();
            
            // 加载分类帖子数
            loadCategoryCounts();
            
            // 加载社区统计信息
            loadCommunityStats();
        });
        
        // 加载帖子列表
        function loadPosts(page) {
            const postList = document.getElementById('post-list');
            postList.innerHTML = '<div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i> 加载中...</div>';
            
            fetch('/community-forum/api/post/list?pageNum=' + page + '&pageSize=10')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        const posts = data.data.list;
                        const total = data.data.total;
                        const pageNum = data.data.pageNum;
                        const pageSize = data.data.pageSize;
                        
                        // 清空加载中提示
                        postList.innerHTML = '';
                        
                        // 添加帖子
                        posts.forEach(post => {
                            postList.appendChild(createPostElement(post));
                        });
                        
                        // 生成分页
                        generatePagination(pageNum, Math.ceil(total / pageSize));
                    } else {
                        postList.innerHTML = '<div class="error-message">加载帖子失败</div>';
                    }
                })
                .catch(error => {
                    console.error('加载帖子失败:', error);
                    postList.innerHTML = '<div class="error-message">加载帖子失败</div>';
                });
        }
        
        // 创建帖子元素
        function createPostElement(post) {
            const postElement = document.createElement('div');
            postElement.className = 'post-card';
            
            const createDate = new Date(post.createTime);
            const formattedDate = createDate.getFullYear() + '-' + String(createDate.getMonth() + 1).padStart(2, '0') + '-' + String(createDate.getDate()).padStart(2, '0');
            
            let postHTML = '';
            postHTML += '<div class="post-header">';
            postHTML += '<img src="' + (post.userAvatar || '/community-forum/static/images/avatar/default.svg') + '" alt="头像" class="avatar">';
            postHTML += '<div>';
            postHTML += '<a href="/community-forum/user/' + post.userId + '" class="user-name">' + post.nickname + '</a>';
            postHTML += '<div class="post-meta">';
            postHTML += formattedDate;
            postHTML += '<a href="/community-forum/category/' + post.categoryId + '" class="post-category">' + post.categoryName + '</a>';
            postHTML += '</div>';
            postHTML += '</div>';
            postHTML += '</div>';
            postHTML += '<div class="post-content">';
            postHTML += '<h2 class="post-title">';
            postHTML += '<a href="/community-forum/post/' + post.postId + '">' + post.title + '</a>';
            postHTML += '</h2>';
            postHTML += '<p class="post-excerpt">' + (post.content.length > 150 ? post.content.substring(0, 150) + '...' : post.content) + '</p>';
            postHTML += '</div>';
            postHTML += '<div class="post-footer">';
            postHTML += '<div class="action view-count">';
            postHTML += '<i class="fas fa-eye"></i> ' + post.viewCount;
            postHTML += '</div>';
            postHTML += '<div class="action like-button" data-post-id="' + post.postId + '">';
            postHTML += '<i class="fas fa-thumbs-up"></i> <span class="like-count">' + post.likeCount + '</span>';
            postHTML += '</div>';
            postHTML += '<div class="action">';
            postHTML += '<i class="fas fa-comment"></i> ' + post.commentCount;
            postHTML += '</div>';
            postHTML += '<div class="action favorite-button" data-post-id="' + post.postId + '">';
            postHTML += '<i class="fas fa-star"></i> <span class="favorite-count">0</span>';
            postHTML += '</div>';
            postHTML += '</div>';
            
            postElement.innerHTML = postHTML;
            return postElement;
        }
        
        // 生成分页
        function generatePagination(currentPage, totalPages) {
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';
            
            // 上一页
            const prevLink = document.createElement('a');
            prevLink.href = 'javascript:void(0);';
            prevLink.innerHTML = '&laquo;';
            if (currentPage > 1) {
                prevLink.onclick = () => loadPosts(currentPage - 1);
            } else {
                prevLink.classList.add('disabled');
            }
            pagination.appendChild(prevLink);
            
            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, startPage + 4);
            
            for (let i = startPage; i <= endPage; i++) {
                const pageLink = document.createElement('a');
                pageLink.href = 'javascript:void(0);';
                pageLink.textContent = i;
                pageLink.onclick = () => loadPosts(i);
                
                if (i === currentPage) {
                    pageLink.classList.add('active');
                }
                
                pagination.appendChild(pageLink);
            }
            
            // 下一页
            const nextLink = document.createElement('a');
            nextLink.href = 'javascript:void(0);';
            nextLink.innerHTML = '&raquo;';
            if (currentPage < totalPages) {
                nextLink.onclick = () => loadPosts(currentPage + 1);
            } else {
                nextLink.classList.add('disabled');
            }
            pagination.appendChild(nextLink);
        }
        
        // 加载热门帖子
        function loadHotPosts() {
            const hotPosts = document.getElementById('hot-posts');
            
            fetch('/community-forum/api/post/list?pageNum=1&pageSize=5&sortBy=viewCount')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        const posts = data.data.list;
                        
                        // 清空加载中提示
                        hotPosts.innerHTML = '';
                        
                        // 添加热门帖子
                        posts.forEach(post => {
                            const postElement = document.createElement('div');
                            postElement.className = 'sidebar-post';
                            
                            let postHTML = '';
                            postHTML += '<a href="/community-forum/post/' + post.postId + '" class="sidebar-post-title">' + post.title + '</a>';
                            postHTML += '<div class="sidebar-post-meta">';
                            postHTML += '<span><i class="fas fa-eye"></i> ' + post.viewCount + '</span>';
                            postHTML += '<span><i class="fas fa-thumbs-up"></i> ' + post.likeCount + '</span>';
                            postHTML += '</div>';
                            
                            postElement.innerHTML = postHTML;
                            hotPosts.appendChild(postElement);
                        });
                    } else {
                        hotPosts.innerHTML = '<div class="error-message">加载热门帖子失败</div>';
                    }
                })
                .catch(error => {
                    console.error('加载热门帖子失败:', error);
                    hotPosts.innerHTML = '<div class="error-message">加载热门帖子失败</div>';
                });
        }
        
        // 加载分类帖子数
        function loadCategoryCounts() {
            fetch('/community-forum/api/category/all')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        const categories = data.data;
                        
                        // 更新分类帖子数
                        categories.forEach(category => {
                            const countElement = document.getElementById(`category-count-${category.categoryId}`);
                            if (countElement) {
                                countElement.textContent = category.postCount || 0;
                            }
                        });
                    }
                })
                .catch(error => console.error('加载分类帖子数失败:', error));
        }
        
        // 加载社区统计信息
        function loadCommunityStats() {
            // 这里可以添加获取社区统计信息的API调用
            // 暂时使用模拟数据
            document.getElementById('user-count').textContent = '1000+';
            document.getElementById('post-count').textContent = '5000+';
            document.getElementById('comment-count').textContent = '10000+';
        }
    </script>
</body>
</html> 
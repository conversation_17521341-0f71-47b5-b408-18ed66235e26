<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.forum.dao.UserDao">
    <!-- 结果映射 -->
    <resultMap id="userResultMap" type="User">
        <id property="userId" column="user_id"/>
        <result property="username" column="username"/>
        <result property="password" column="password"/>
        <result property="email" column="email"/>
        <result property="avatar" column="avatar"/>
        <result property="nickname" column="nickname"/>
        <result property="bio" column="bio"/>
        <result property="status" column="status"/>
        <result property="role" column="role"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    
    <!-- 基础列 -->
    <sql id="baseColumns">
        user_id, username, password, email, avatar, nickname, bio, status, role, create_time, update_time
    </sql>
    
    <!-- 根据ID获取用户 -->
    <select id="getUserById" resultMap="userResultMap" parameterType="long">
        SELECT <include refid="baseColumns"/>
        FROM user
        WHERE user_id = #{userId}
    </select>
    
    <!-- 根据用户名获取用户 -->
    <select id="getUserByUsername" resultMap="userResultMap" parameterType="string">
        SELECT <include refid="baseColumns"/>
        FROM user
        WHERE username = #{username}
    </select>
    
    <!-- 根据邮箱获取用户 -->
    <select id="getUserByEmail" resultMap="userResultMap" parameterType="string">
        SELECT <include refid="baseColumns"/>
        FROM user
        WHERE email = #{email}
    </select>
    
    <!-- 插入新用户 -->
    <insert id="insertUser" parameterType="User" useGeneratedKeys="true" keyProperty="userId">
        INSERT INTO user (username, password, email, avatar, nickname, bio, status, role, create_time, update_time)
        VALUES (#{username}, #{password}, #{email}, #{avatar}, #{nickname}, #{bio}, #{status}, #{role}, #{createTime}, #{updateTime})
    </insert>
    
    <!-- 更新用户信息 -->
    <update id="updateUser" parameterType="User">
        UPDATE user
        <set>
            <if test="username != null">username = #{username},</if>
            <if test="email != null">email = #{email},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="nickname != null">nickname = #{nickname},</if>
            <if test="bio != null">bio = #{bio},</if>
            <if test="status != null">status = #{status},</if>
            <if test="role != null">role = #{role},</if>
            update_time = #{updateTime}
        </set>
        WHERE user_id = #{userId}
    </update>
    
    <!-- 更新用户密码 -->
    <update id="updatePassword">
        UPDATE user
        SET password = #{password}, update_time = NOW()
        WHERE user_id = #{userId}
    </update>
    
    <!-- 删除用户 -->
    <delete id="deleteUser" parameterType="long">
        DELETE FROM user
        WHERE user_id = #{userId}
    </delete>
    
    <!-- 获取用户列表（分页） -->
    <select id="getUserList" resultMap="userResultMap">
        SELECT <include refid="baseColumns"/>
        FROM user
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>
    
    <!-- 获取用户总数 -->
    <select id="getUserCount" resultType="int">
        SELECT COUNT(*)
        FROM user
    </select>
</mapper> 
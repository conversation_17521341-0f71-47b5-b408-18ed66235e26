package com.forum.interceptor;

import com.forum.entity.User;
import com.forum.exception.UnauthorizedException;
import com.forum.service.UserService;
import com.forum.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * JWT拦截器
 */
@Component
public class JwtInterceptor implements HandlerInterceptor {
    
    @Autowired
    private UserService userService;
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 从请求头中获取token
        String authHeader = request.getHeader("Authorization");
        
        // 如果没有Authorization请求头，则抛出异常
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            throw new UnauthorizedException("请先登录");
        }
        
        // 获取token
        String token = authHeader.substring(7);
        
        // 如果token为空，则抛出异常
        if (!StringUtils.hasText(token)) {
            throw new UnauthorizedException("请先登录");
        }
        
        // 验证token
        if (!JwtUtil.validateToken(token)) {
            throw new UnauthorizedException("登录已过期，请重新登录");
        }
        
        // 从token中获取用户ID
        Long userId = JwtUtil.getUserIdFromToken(token);
        
        // 获取用户信息
        User user = userService.getUserById(userId);
        
        // 将用户ID和角色设置到请求属性中，供后续使用
        request.setAttribute("userId", userId);
        request.setAttribute("userRole", user.getRole());
        
        return true;
    }
}
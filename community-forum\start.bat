@echo off
echo ========================================
echo 社区论坛启动脚本
echo ========================================

echo.
echo 1. 检查 MySQL 连接...
mysql -u root -p123456 -e "SELECT 1;" >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 无法连接到 MySQL 数据库
    echo 请确保：
    echo - MySQL 服务正在运行
    echo - 用户名: root, 密码: 123456
    echo - 端口: 3306
    pause
    exit /b 1
)
echo [成功] MySQL 连接正常

echo.
echo 2. 初始化数据库...
mysql -u root -p123456 < init-db.sql
if %errorlevel% neq 0 (
    echo [错误] 数据库初始化失败
    pause
    exit /b 1
)
echo [成功] 数据库初始化完成

echo.
echo 3. 编译项目...
mvn clean compile
if %errorlevel% neq 0 (
    echo [错误] 项目编译失败
    pause
    exit /b 1
)
echo [成功] 项目编译完成

echo.
echo 4. 启动 Tomcat 服务器...
echo 项目将在 http://localhost:8080/community-forum 启动
echo 按 Ctrl+C 停止服务器
echo.
mvn tomcat7:run

pause

package com.forum.config;

import com.forum.interceptor.JwtInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {
    
    @Autowired
    private JwtInterceptor jwtInterceptor;
    
    /**
     * 配置拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 添加JWT拦截器
        registry.addInterceptor(jwtInterceptor)
                .addPathPatterns("/api/**") // 拦截所有API请求
                .excludePathPatterns(
                        "/api/user/register", // 排除注册接口
                        "/api/user/login", // 排除登录接口
                        "/api/user/check-username", // 排除检查用户名接口
                        "/api/user/check-email", // 排除检查邮箱接口
                        "/api/category/all", // 排除获取所有分类接口
                        "/api/category/enabled", // 排除获取启用分类接口
                        "/api/category/{categoryId}", // 排除获取分类详情接口
                        "/api/post/list", // 排除获取帖子列表接口
                        "/api/post/{postId}", // 排除获取帖子详情接口
                        "/api/post/category/**", // 排除根据分类获取帖子接口
                        "/api/post/search", // 排除搜索帖子接口
                        "/api/comment/post/**" // 排除获取帖子评论接口
                );
    }
    
    /**
     * 配置跨域
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**") // 允许所有请求路径
                .allowedOrigins("*") // 允许所有来源
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS") // 允许的HTTP方法
                .allowedHeaders("*") // 允许所有请求头
                .exposedHeaders("Authorization") // 暴露Authorization响应头
                .maxAge(3600); // 预检请求的有效期，单位为秒
    }
    
    /**
     * 配置静态资源处理
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置静态资源路径
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/");
    }
} 